/*     */ package com.baaon.v2;
/*     */ 
/*     */ import com.baaon.SecuritySHA1Util;
/*     */ import com.intellij.ide.util.PropertiesComponent;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.OutputStream;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.URL;
/*     */ import java.nio.charset.StandardCharsets;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AuthenticationManager
/*     */ {
/*  21 */   private static final Logger LOG = Logger.getInstance(AuthenticationManager.class);
/*     */   private static final String FORMAL_VERIFICATION_STATUS_KEY = "augment.formal.verification.status.v2";
/*     */   private static final String AUTH_CODE_STORAGE_KEY = "augment.auth.code.storage.v2";
/*     */   private static final String EMAIL_ADDRESS_STORAGE_KEY = "augment.email.address.storage.v2";
/*     */   private static final String FIXED_TRIAL_CODE = "1024";
/*     */   private static final String FORMAL_VERIFICATION_API_URL = "http://*************:82/api/aug/code/active";
/*  27 */   private static final AuthenticationManager INSTANCE = new AuthenticationManager();
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public static AuthenticationManager getInstance() {
/*  33 */     return INSTANCE;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean verifyTrialCode(@NotNull String trialCode) {
/*  38 */     boolean isValid = "1024".equals(trialCode.trim());
/*  39 */     LOG.info("试用验证码验证结果: " + isValid + " (输入: " + trialCode + ", 期望: 1024)");
/*  40 */     return isValid;
/*     */   }
/*     */   
/*     */   public boolean activateTrialMode() {
/*  44 */     LOG.info("直接激活试用模式，使用固定验证码: 1024");
/*  45 */     return verifyTrialCode("1024");
/*     */   }
/*     */   
/*     */   public boolean verifyFormalCode(@NotNull String formalCode) throws Exception {
/*     */     boolean var11;
/*  50 */     LOG.info("调用正式验证API: http://*************:82/api/aug/code/active");
/*  51 */     long time = System.currentTimeMillis();
/*  52 */     String encode = SecuritySHA1Util.shaEncode("" + time + "10f2f96d89a32941edf01bxcz0a076f10");
/*  53 */     URL url = new URL("http://*************:82/api/aug/code/active?ts=" + time + "&sign=" + encode);
/*  54 */     HttpURLConnection connection = (HttpURLConnection)url.openConnection();
/*     */ 
/*     */     
/*     */     try {
/*  58 */       connection.setRequestMethod("POST");
/*  59 */       connection.setConnectTimeout(10000);
/*  60 */       connection.setReadTimeout(10000);
/*  61 */       connection.setDoOutput(true);
/*  62 */       connection.setRequestProperty("Content-Type", "application/json");
/*  63 */       connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
/*  64 */       String requestBody = buildVerificationRequestBody(formalCode);
/*  65 */       LOG.info("正式验证API请求体: " + requestBody);
/*  66 */       OutputStream os = connection.getOutputStream();
/*     */       
/*     */       try {
/*  69 */         byte[] input = requestBody.getBytes(StandardCharsets.UTF_8);
/*  70 */         os.write(input, 0, input.length);
/*  71 */       } catch (Throwable var20) {
/*  72 */         if (os != null) {
/*     */           try {
/*  74 */             os.close();
/*  75 */           } catch (Throwable var19) {
/*  76 */             var20.addSuppressed(var19);
/*     */           } 
/*     */         }
/*     */         
/*  80 */         throw var20;
/*     */       } 
/*     */       
/*  83 */       if (os != null) {
/*  84 */         os.close();
/*     */       }
/*     */       
/*  87 */       int responseCode = connection.getResponseCode();
/*  88 */       LOG.info("正式验证API响应码: " + responseCode);
/*  89 */       if (responseCode != 200) {
/*  90 */         LOG.warn("正式验证API调用失败，响应码: " + responseCode);
/*  91 */         boolean var25 = false;
/*  92 */         return var25;
/*     */       } 
/*     */       
/*  95 */       BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
/*     */       
/*     */       try {
/*  98 */         StringBuilder response = new StringBuilder();
/*     */         
/*     */         String line;
/* 101 */         while ((line = reader.readLine()) != null) {
/* 102 */           response.append(line);
/*     */         }
/*     */         
/* 105 */         String responseBody = response.toString();
/* 106 */         LOG.info("正式验证API响应: " + responseBody);
/* 107 */         boolean isValid = parseVerificationResponse(responseBody);
/* 108 */         LOG.info("正式验证码验证结果: " + isValid);
/* 109 */         var11 = isValid;
/* 110 */       } catch (Throwable var21) {
/*     */         try {
/* 112 */           reader.close();
/* 113 */         } catch (Throwable var18) {
/* 114 */           var21.addSuppressed(var18);
/*     */         } 
/*     */         
/* 117 */         throw var21;
/*     */       } 
/*     */       
/* 120 */       reader.close();
/*     */     } finally {
/* 122 */       connection.disconnect();
/*     */     } 
/*     */     
/* 125 */     return var11;
/*     */   }
/*     */ 
/*     */   
/*     */   private String buildVerificationRequestBody(@NotNull String code) {
/* 130 */     String deviceId = SessionId.INSTANCE.getSessionId();
/* 131 */     String osName = System.getProperty("os.name", "Unknown");
/* 132 */     String osVersion = System.getProperty("os.version", "Unknown");
/* 133 */     String operatingSystem = osName + " " + osName;
/* 134 */     StringBuilder json = new StringBuilder();
/* 135 */     json.append("{");
/* 136 */     json.append("\"code\":\"").append(escapeJson(code)).append("\",");
/* 137 */     json.append("\"deviceId\":\"").append(escapeJson(deviceId)).append("\",");
/* 138 */     json.append("\"deviceType\":\"Desktop\",");
/* 139 */     json.append("\"operatingSystem\":\"").append(escapeJson(operatingSystem)).append("\",");
/* 140 */     json.append("\"browserInfo\":\"IntelliJ IDEA Plugin\",");
/* 141 */     json.append("\"userAgent\":\"Augment-Assistant-Plugin\"");
/* 142 */     json.append("}");
/* 143 */     return json.toString();
/*     */   }
/*     */   
/*     */   private boolean parseVerificationResponse(@NotNull String responseBody) {
/*     */     try {
/* 148 */       if (!responseBody.contains("\"success\":true")) {
/* 149 */         LOG.warn("外层验证失败，响应: " + responseBody);
/* 150 */         return false;
/*     */       } 
/* 152 */       int dataIndex = responseBody.indexOf("\"data\":");
/* 153 */       if (dataIndex == -1) {
/* 154 */         LOG.warn("响应中未找到data字段");
/* 155 */         return false;
/*     */       } 
/* 157 */       String dataSection = responseBody.substring(dataIndex);
/* 158 */       boolean dataSuccess = dataSection.contains("\"success\":true");
/* 159 */       if (!dataSuccess) {
/* 160 */         LOG.warn("data.success为false，验证失败");
/* 161 */         if (dataSection.contains("\"remainingAttempts\"")) {
/* 162 */           LOG.info("响应包含剩余尝试次数信息");
/*     */         }
/*     */       } 
/*     */       
/* 166 */       return dataSuccess;
/*     */     
/*     */     }
/* 169 */     catch (Exception e) {
/* 170 */       LOG.error("解析验证响应失败", e);
/* 171 */       return false;
/*     */     } 
/*     */   }
/*     */   
/*     */   private String escapeJson(@NotNull String str) {
/* 176 */     return str.replace("\\", "\\\\").replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r").replace("\t", "\\t");
/*     */   }
/*     */   
/*     */   public void saveFormalVerificationStatus(boolean verified) {
/* 180 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 181 */     properties.setValue("augment.formal.verification.status.v2", verified);
/* 182 */     LOG.info("保存正式验证状态: " + verified);
/* 183 */     if (verified) {
/* 184 */       TrialSessionManager.getInstance().clearTrialData();
/* 185 */       LOG.info("正式验证成功，已清理试用数据");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean isFormallyVerified() {
/* 191 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 192 */     return properties.getBoolean("augment.formal.verification.status.v2", false);
/*     */   }
/*     */   
/*     */   public void clearFormalVerificationStatus() {
/* 196 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 197 */     properties.unsetValue("augment.formal.verification.status.v2");
/* 198 */     LOG.info("已清除正式验证状态");
/*     */   }
/*     */   @NotNull
/*     */   public String getAuthenticationStatus() {
/* 202 */     if (isFormallyVerified())
/* 203 */       return "正式认证"; 
/* 204 */     if (TrialSessionManager.getInstance().hasValidTrialSession()) {
/* 205 */       int remainingDays = TrialSessionManager.getInstance().getRemainingDays();
/* 206 */       return "试用认证 (剩余 " + remainingDays + " 天)";
/*     */     } 
/* 208 */     return "未认证";
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean hasValidAuthentication() {
/* 213 */     return (isFormallyVerified() || TrialSessionManager.getInstance().hasValidTrialSession());
/*     */   }
/*     */   
/*     */   public void saveAuthCode(@NotNull String authCode) {
/* 217 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 218 */     properties.setValue("augment.auth.code.storage.v2", authCode.trim());
/* 219 */     LOG.info("保存授权码: " + authCode.trim());
/*     */   }
/*     */   
/*     */   public String getSavedAuthCode() {
/* 223 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 224 */     String authCode = properties.getValue("augment.auth.code.storage.v2");
/* 225 */     if (authCode != null && !authCode.trim().isEmpty()) {
/* 226 */       LOG.info("获取保存的授权码: " + authCode);
/* 227 */       return authCode.trim();
/*     */     } 
/* 229 */     LOG.info("未找到保存的授权码");
/* 230 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public void clearSavedAuthCode() {
/* 235 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 236 */     properties.unsetValue("augment.auth.code.storage.v2");
/* 237 */     LOG.info("已清除保存的授权码");
/*     */   }
/*     */   
/*     */   public boolean hasSavedAuthCode() {
/* 241 */     String authCode = getSavedAuthCode();
/* 242 */     return (authCode != null && !authCode.trim().isEmpty());
/*     */   }
/*     */   
/*     */   public void saveEmailAddress(@NotNull String emailAddress) {
/* 246 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 247 */     properties.setValue("augment.email.address.storage.v2", emailAddress.trim());
/* 248 */     LOG.info("保存邮箱地址: " + emailAddress.trim());
/*     */   }
/*     */   
/*     */   public String getSavedEmailAddress() {
/* 252 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 253 */     String emailAddress = properties.getValue("augment.email.address.storage.v2");
/* 254 */     if (emailAddress != null && !emailAddress.trim().isEmpty()) {
/* 255 */       LOG.info("获取保存的邮箱地址: " + emailAddress);
/* 256 */       return emailAddress.trim();
/*     */     } 
/* 258 */     LOG.info("未找到保存的邮箱地址");
/* 259 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public void clearSavedEmailAddress() {
/* 264 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 265 */     properties.unsetValue("augment.email.address.storage.v2");
/* 266 */     LOG.info("已清除保存的邮箱地址");
/*     */   }
/*     */   
/*     */   public boolean hasSavedEmailAddress() {
/* 270 */     String emailAddress = getSavedEmailAddress();
/* 271 */     return (emailAddress != null && !emailAddress.trim().isEmpty());
/*     */   }
/*     */   
/*     */   public void clearAllSavedData() {
/* 275 */     clearFormalVerificationStatus();
/* 276 */     clearSavedAuthCode();
/* 277 */     clearSavedEmailAddress();
/* 278 */     LOG.info("已清除所有保存的数据");
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\AuthenticationManager.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */