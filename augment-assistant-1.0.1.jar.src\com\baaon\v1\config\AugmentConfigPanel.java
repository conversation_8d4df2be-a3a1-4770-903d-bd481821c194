/*     */ package com.baaon.v1.config;
/*     */ 
/*     */ import com.baaon.SecuritySHA1Util;
/*     */ import com.baaon.v1.SessionId;
/*     */ import com.baaon.v1.SessionIdReplacer;
/*     */ import com.intellij.ide.util.PropertiesComponent;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import com.intellij.openapi.ui.Messages;
/*     */ import com.intellij.ui.components.JBLabel;
/*     */ import com.intellij.ui.components.JBTextField;
/*     */ import com.intellij.util.ui.FormBuilder;
/*     */ import com.intellij.util.ui.JBUI;
/*     */ import java.awt.Color;
/*     */ import java.awt.Component;
/*     */ import java.awt.FlowLayout;
/*     */ import java.awt.Font;
/*     */ import java.awt.Toolkit;
/*     */ import java.awt.datatransfer.ClipboardOwner;
/*     */ import java.awt.datatransfer.StringSelection;
/*     */ import java.awt.event.ActionEvent;
/*     */ import java.awt.event.ActionListener;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.InputStreamReader;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.URL;
/*     */ import java.net.URLEncoder;
/*     */ import java.nio.charset.StandardCharsets;
/*     */ import javax.swing.JButton;
/*     */ import javax.swing.JComponent;
/*     */ import javax.swing.JPanel;
/*     */ import javax.swing.SwingUtilities;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AugmentConfigPanel
/*     */ {
/* 102 */   private static final Logger LOG = Logger.getInstance(AugmentConfigPanel.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JPanel mainPanel;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JBTextField sessionIdField;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JBLabel sessionIdSourceLabel;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JBTextField verificationCodeField;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JButton verifyButton;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JBLabel verificationStatusLabel;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JButton generateButton;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private JButton resetButton;
/*     */ 
/*     */ 
/*     */   
/*     */   private JButton copyButton;
/*     */ 
/*     */ 
/*     */   
/*     */   private String originalSessionId;
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean modified = false;
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isVerified = false;
/*     */ 
/*     */ 
/*     */   
/*     */   private static final String VERIFICATION_API_URL = "https://baaon.com/api/aug/verify";
/*     */ 
/*     */ 
/*     */   
/*     */   private static final String VERIFICATION_STATUS_KEY = "augment.verification.status";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AugmentConfigPanel() {
/* 172 */     initializeComponents();
/* 173 */     setupLayout();
/* 174 */     setupEventHandlers();
/* 175 */     loadVerificationStatus();
/* 176 */     loadCurrentSettings();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void initializeComponents() {
/* 194 */     this.verificationCodeField = new JBTextField();
/* 195 */     this.verificationCodeField.setColumns(10);
/*     */ 
/*     */     
/* 198 */     this.verifyButton = new JButton("验证");
/*     */ 
/*     */     
/* 201 */     this.verificationStatusLabel = new JBLabel("请输入验证码以启用SessionId功能");
/* 202 */     this.verificationStatusLabel.setFont(this.verificationStatusLabel.getFont().deriveFont(2));
/* 203 */     this.verificationStatusLabel.setForeground(Color.ORANGE);
/*     */ 
/*     */     
/* 206 */     this.sessionIdField = new JBTextField();
/* 207 */     this.sessionIdField.setEditable(false);
/* 208 */     this.sessionIdField.setFont(new Font("Monospaced", 0, 12));
/*     */ 
/*     */     
/* 211 */     this.sessionIdSourceLabel = new JBLabel();
/* 212 */     this.sessionIdSourceLabel.setFont(this.sessionIdSourceLabel.getFont().deriveFont(2));
/*     */ 
/*     */     
/* 215 */     this.generateButton = new JButton("生成新的SessionId");
/* 216 */     this.resetButton = new JButton("重置SessionId");
/* 217 */     this.copyButton = new JButton("复制到剪贴板");
/*     */ 
/*     */     
/* 220 */     updateButtonStates();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setupLayout() {
/* 241 */     JPanel verificationPanel = new JPanel(new FlowLayout(0, 5, 0));
/* 242 */     verificationPanel.add((Component)this.verificationCodeField);
/* 243 */     verificationPanel.add(this.verifyButton);
/*     */ 
/*     */     
/* 246 */     JPanel buttonPanel = new JPanel(new FlowLayout(0, 5, 0));
/* 247 */     buttonPanel.add(this.generateButton);
/* 248 */     buttonPanel.add(this.resetButton);
/* 249 */     buttonPanel.add(this.copyButton);
/*     */ 
/*     */     
/* 252 */     this
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 260 */       .mainPanel = FormBuilder.createFormBuilder().addLabeledComponent("验证码:", verificationPanel, 1, false).addComponentToRightColumn((JComponent)this.verificationStatusLabel, 1).addSeparator().addLabeledComponent("当前SessionId:", (JComponent)this.sessionIdField, 1, false).addComponentToRightColumn((JComponent)this.sessionIdSourceLabel, 1).addComponent(buttonPanel, 1).addComponentFillVertically(new JPanel(), 0).getPanel();
/*     */ 
/*     */     
/* 263 */     this.mainPanel.setBorder(JBUI.Borders.empty(10));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setupEventHandlers() {
/* 286 */     this.verifyButton.addActionListener(new ActionListener() {
/*     */           public void actionPerformed(ActionEvent e) {
/* 288 */             AugmentConfigPanel.this.verifyCode();
/*     */           }
/*     */         });
/*     */ 
/*     */     
/* 293 */     this.verificationCodeField.addActionListener(new ActionListener() {
/*     */           public void actionPerformed(ActionEvent e) {
/* 295 */             AugmentConfigPanel.this.verifyCode();
/*     */           }
/*     */         });
/*     */ 
/*     */     
/* 300 */     this.generateButton.addActionListener(new ActionListener() {
/*     */           public void actionPerformed(ActionEvent e) {
/* 302 */             if (AugmentConfigPanel.this.isVerified) {
/* 303 */               AugmentConfigPanel.this.generateNewSessionId();
/*     */             } else {
/* 305 */               AugmentConfigPanel.this.showVerificationRequiredMessage();
/*     */             } 
/*     */           }
/*     */         });
/*     */ 
/*     */     
/* 311 */     this.resetButton.addActionListener(new ActionListener() {
/*     */           public void actionPerformed(ActionEvent e) {
/* 313 */             if (AugmentConfigPanel.this.isVerified) {
/* 314 */               AugmentConfigPanel.this.resetSessionId();
/*     */             } else {
/* 316 */               AugmentConfigPanel.this.showVerificationRequiredMessage();
/*     */             } 
/*     */           }
/*     */         });
/*     */ 
/*     */     
/* 322 */     this.copyButton.addActionListener(new ActionListener() {
/*     */           public void actionPerformed(ActionEvent e) {
/* 324 */             if (AugmentConfigPanel.this.isVerified) {
/* 325 */               AugmentConfigPanel.this.copySessionIdToClipboard();
/*     */             } else {
/* 327 */               AugmentConfigPanel.this.showVerificationRequiredMessage();
/*     */             } 
/*     */           }
/*     */         });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void loadCurrentSettings() {
/*     */     try {
/* 353 */       SessionId sessionIdInstance = SessionId.INSTANCE;
/*     */ 
/*     */       
/* 356 */       String currentSessionId = sessionIdInstance.c1();
/* 357 */       String source = sessionIdInstance.c4();
/*     */ 
/*     */       
/* 360 */       this.sessionIdField.setText(currentSessionId);
/* 361 */       this.sessionIdSourceLabel.setText("来源: " + getSourceDescription(source));
/*     */ 
/*     */       
/* 364 */       this.originalSessionId = currentSessionId;
/* 365 */       this.modified = false;
/*     */       
/* 367 */       LOG.info("加载当前SessionId配置: " + currentSessionId + " (来源: " + source + ")");
/* 368 */     } catch (Exception e) {
/*     */       
/* 370 */       LOG.error("加载SessionId配置失败", e);
/* 371 */       this.sessionIdField.setText("加载失败");
/* 372 */       this.sessionIdSourceLabel.setText("来源: 未知");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getSourceDescription(String source) {
/* 394 */     switch (source) {
/*     */       case "PermanentInstallationID":
/* 396 */         return "永久安装ID";
/*     */       
/*     */       case "PropertiesComponent":
/* 399 */         return "已保存的配置";
/*     */       
/*     */       case "Generated":
/* 402 */         return "自动生成";
/*     */     } 
/*     */     
/* 405 */     return source;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void generateNewSessionId() {
/*     */     try {
/* 438 */       String newSessionId = SessionId.INSTANCE.c2();
/*     */ 
/*     */       
/* 441 */       this.sessionIdField.setText(newSessionId);
/* 442 */       this.sessionIdSourceLabel.setText("来源: " + getSourceDescription("PropertiesComponent"));
/* 443 */       this.modified = true;
/*     */       
/* 445 */       LOG.info("生成新的SessionId: " + newSessionId);
/*     */ 
/*     */       
/*     */       try {
/* 449 */         SessionIdReplacer replacer = new SessionIdReplacer();
/* 450 */         boolean success = replacer.a2();
/*     */         
/* 452 */         if (success) {
/*     */           
/* 454 */           Messages.showInfoMessage("新的SessionId已生成并立即生效！\n\n" + newSessionId, "SessionId生成成功");
/* 455 */           LOG.info("SessionId替换成功，新SessionId已生效");
/*     */         } else {
/*     */           
/* 458 */           Messages.showWarningDialog("新的SessionId已生成并保存，但替换失败。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。", "SessionId生成成功，但替换失败");
/* 459 */           LOG.warn("SessionId生成成功，但替换失败");
/*     */         } 
/* 461 */       } catch (Exception replaceException) {
/*     */         
/* 463 */         LOG.error("调用SessionIdReplacer失败", replaceException);
/* 464 */         Messages.showWarningDialog("新的SessionId已生成并保存，但替换过程出现异常。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。\n\n错误详情: " + replaceException.getMessage(), "SessionId生成成功，但替换异常");
/*     */       } 
/* 466 */     } catch (Exception e) {
/*     */       
/* 468 */       LOG.error("生成SessionId失败", e);
/* 469 */       Messages.showErrorDialog("生成SessionId失败: " + e.getMessage(), "错误");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void resetSessionId() {
/* 481 */     int result = Messages.showYesNoDialog("确定要重置SessionId吗？\n\n这将清除当前保存的SessionId，系统将使用永久安装ID或重新生成。", "确认重置", Messages.getQuestionIcon());
/* 482 */     if (result == 0) {
/*     */       
/*     */       try {
/* 485 */         SessionId.INSTANCE.clearStoredSessionId();
/* 486 */         loadCurrentSettings();
/* 487 */         this.modified = true;
/* 488 */         LOG.info("SessionId已重置");
/*     */ 
/*     */         
/*     */         try {
/* 492 */           SessionIdReplacer replacer = new SessionIdReplacer();
/* 493 */           boolean success = replacer.a2();
/* 494 */           if (success) {
/* 495 */             Messages.showInfoMessage("SessionId已重置并立即生效！\n\n当前SessionId: " + this.sessionIdField.getText(), "重置成功");
/* 496 */             LOG.info("SessionId重置成功，新SessionId已生效");
/*     */           } else {
/* 498 */             Messages.showWarningDialog("SessionId已重置，但替换失败。\n\n当前SessionId: " + this.sessionIdField.getText() + "\n\n请重启IDE以确保重置后的SessionId生效。", "重置成功，但替换失败");
/* 499 */             LOG.warn("SessionId重置成功，但替换失败");
/*     */           } 
/* 501 */         } catch (Exception replaceException) {
/* 502 */           LOG.error("调用SessionIdReplacer失败", replaceException);
/* 503 */           Messages.showWarningDialog("SessionId已重置，但替换过程出现异常。\n\n当前SessionId: " + this.sessionIdField.getText() + "\n\n请重启IDE以确保重置后的SessionId生效。\n\n错误详情: " + replaceException.getMessage(), "重置成功，但替换异常");
/*     */         } 
/* 505 */       } catch (Exception e) {
/* 506 */         LOG.error("重置SessionId失败", e);
/* 507 */         Messages.showErrorDialog("重置SessionId失败: " + e.getMessage(), "错误");
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void copySessionIdToClipboard() {
/*     */     try {
/* 519 */       String sessionId = this.sessionIdField.getText();
/* 520 */       if (sessionId != null && !sessionId.trim().isEmpty()) {
/*     */         
/* 522 */         Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(sessionId), (ClipboardOwner)null);
/* 523 */         Messages.showInfoMessage("SessionId已复制到剪贴板！", "复制成功");
/* 524 */         LOG.info("SessionId已复制到剪贴板");
/*     */       } 
/* 526 */     } catch (Exception e) {
/* 527 */       LOG.error("复制SessionId失败", e);
/* 528 */       Messages.showErrorDialog("复制SessionId失败: " + e.getMessage(), "错误");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JPanel getMainPanel() {
/* 540 */     return this.mainPanel;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isModified() {
/* 551 */     return this.modified;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void apply() {
/* 560 */     this.modified = false;
/* 561 */     this.originalSessionId = this.sessionIdField.getText();
/* 562 */     LOG.info("配置已应用");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void reset() {
/* 571 */     loadCurrentSettings();
/* 572 */     LOG.info("配置已重置");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyCode() {
/* 582 */     String inputCode = this.verificationCodeField.getText().trim();
/* 583 */     if (inputCode.isEmpty()) {
/* 584 */       Messages.showWarningDialog("请输入验证码！", "验证码为空");
/*     */     } else {
/*     */       
/* 587 */       this.verifyButton.setEnabled(false);
/* 588 */       this.verifyButton.setText("验证中...");
/* 589 */       this.verificationStatusLabel.setText("正在验证验证码，请稍候...");
/* 590 */       this.verificationStatusLabel.setForeground(Color.BLUE);
/*     */ 
/*     */       
/* 593 */       SwingUtilities.invokeLater(() -> (new Thread(())).start());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void showVerificationRequiredMessage() {
/* 643 */     Messages.showWarningDialog("请先输入正确的验证码以启用SessionId功能！", "需要验证");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void updateButtonStates() {
/* 653 */     this.generateButton.setEnabled(this.isVerified);
/* 654 */     this.resetButton.setEnabled(this.isVerified);
/* 655 */     this.copyButton.setEnabled(this.isVerified);
/*     */ 
/*     */     
/* 658 */     if (this.isVerified) {
/* 659 */       this.generateButton.setToolTipText("生成新的SessionId");
/* 660 */       this.resetButton.setToolTipText("重置SessionId");
/* 661 */       this.copyButton.setToolTipText("复制SessionId到剪贴板");
/*     */     } else {
/* 663 */       this.generateButton.setToolTipText("请先验证验证码");
/* 664 */       this.resetButton.setToolTipText("请先验证验证码");
/* 665 */       this.copyButton.setToolTipText("请先验证验证码");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean callVerificationAPI(String code) throws Exception {
/*     */     boolean result;
/* 680 */     if (code.equals("error")) {
/* 681 */       return false;
/*     */     }
/* 683 */     long time = System.currentTimeMillis();
/* 684 */     String encode = SecuritySHA1Util.shaEncode("" + time + "10f2f96d89a32941edf01bxcz0a076f10");
/* 685 */     String urlString = "https://baaon.com/api/aug/verify?ts=" + time + "&sign=" + encode + "&code=" + URLEncoder.encode(code, StandardCharsets.UTF_8);
/* 686 */     LOG.info("调用验证API: " + urlString);
/* 687 */     URL url = new URL(urlString);
/* 688 */     HttpURLConnection connection = (HttpURLConnection)url.openConnection();
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 693 */       connection.setRequestMethod("GET");
/* 694 */       connection.setConnectTimeout(10000);
/* 695 */       connection.setReadTimeout(10000);
/* 696 */       connection.setRequestProperty("Accept", "application/json");
/* 697 */       connection.setRequestProperty("User-Agent", "AugmentAssistant/1.0");
/*     */ 
/*     */       
/* 700 */       int responseCode = connection.getResponseCode();
/* 701 */       LOG.info("API响应码: " + responseCode);
/* 702 */       if (responseCode != 200) {
/* 703 */         throw new Exception("API请求失败，响应码: " + responseCode);
/*     */       }
/*     */ 
/*     */       
/* 707 */       BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
/* 708 */       StringBuilder response = new StringBuilder();
/*     */       
/*     */       String line;
/* 711 */       while ((line = reader.readLine()) != null) {
/* 712 */         response.append(line);
/*     */       }
/*     */       
/* 715 */       reader.close();
/* 716 */       String responseBody = response.toString();
/* 717 */       LOG.info("API响应内容: " + responseBody);
/*     */ 
/*     */       
/* 720 */       boolean apiResult = (responseBody.contains("\"data\":true") || responseBody.contains("\"data\": true"));
/* 721 */       LOG.info("验证结果: " + apiResult);
/* 722 */       result = apiResult;
/*     */     } finally {
/* 724 */       connection.disconnect();
/*     */     } 
/*     */     
/* 727 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void loadVerificationStatus() {
/* 737 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 738 */     boolean savedVerificationStatus = properties.getBoolean("augment.verification.status", false);
/* 739 */     if (savedVerificationStatus) {
/* 740 */       this.isVerified = true;
/* 741 */       this.verificationStatusLabel.setText("已验证！SessionId功能已启用");
/* 742 */       this.verificationStatusLabel.setForeground(Color.GREEN);
/* 743 */       this.verificationCodeField.setText("已验证");
/* 744 */       this.verificationCodeField.setEnabled(false);
/* 745 */       this.verifyButton.setEnabled(false);
/* 746 */       this.verifyButton.setText("已验证");
/* 747 */       updateButtonStates();
/* 748 */       LOG.info("加载已保存的验证状态：已验证");
/*     */     } else {
/* 750 */       this.isVerified = false;
/* 751 */       this.verificationStatusLabel.setText("请输入验证码以启用SessionId功能");
/* 752 */       this.verificationStatusLabel.setForeground(Color.ORANGE);
/* 753 */       updateButtonStates();
/* 754 */       LOG.info("加载已保存的验证状态：未验证");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void saveVerificationStatus(boolean verified) {
/* 767 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 768 */     properties.setValue("augment.verification.status", verified);
/* 769 */     LOG.info("保存验证状态: " + verified);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void clearVerificationStatus() {
/* 779 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 780 */     properties.unsetValue("augment.verification.status");
/* 781 */     this.isVerified = false;
/* 782 */     this.verificationCodeField.setText("");
/* 783 */     this.verificationCodeField.setEnabled(true);
/* 784 */     this.verifyButton.setEnabled(true);
/* 785 */     this.verifyButton.setText("验证");
/* 786 */     this.verificationStatusLabel.setText("请输入验证码以启用SessionId功能");
/* 787 */     this.verificationStatusLabel.setForeground(Color.ORANGE);
/* 788 */     updateButtonStates();
/* 789 */     LOG.info("已清除验证状态");
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v1\config\AugmentConfigPanel.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */