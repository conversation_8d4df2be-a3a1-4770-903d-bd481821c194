/*     */ package com.baaon.v1;
/*     */ 
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import com.intellij.openapi.project.Project;
/*     */ import com.intellij.openapi.startup.ProjectActivity;
/*     */ import kotlin.Unit;
/*     */ import kotlin.coroutines.Continuation;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AugmentStartupActivity
/*     */   implements ProjectActivity
/*     */ {
/*  43 */   private static final Logger LOG = Logger.getInstance(AugmentStartupActivity.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Nullable
/*     */   public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
/*  87 */     if (project == null) {
/*  88 */       throw new IllegalArgumentException("project cannot be null");
/*     */     }
/*     */ 
/*     */     
/*  92 */     if (continuation == null) {
/*  93 */       throw new IllegalArgumentException("continuation cannot be null");
/*     */     }
/*     */ 
/*     */     
/*  97 */     LOG.info("开始为项目 [" + project.getName() + "] 替换目标插件类...");
/*     */ 
/*     */     
/*     */     try {
/* 101 */       SessionIdReplacer replacer = new SessionIdReplacer();
/*     */ 
/*     */       
/* 104 */       if (replacer.a2()) {
/* 105 */         LOG.info("项目 [" + project.getName() + "] 的SessionId类替换成功");
/*     */       } else {
/* 107 */         LOG.warn("项目 [" + project.getName() + "] 的SessionId类替换失败");
/*     */       } 
/* 109 */     } catch (Exception e) {
/*     */       
/* 111 */       LOG.error("项目 [" + project.getName() + "] 的SessionId替换过程中发生错误", e);
/*     */     } 
/*     */ 
/*     */     
/* 115 */     return Unit.INSTANCE;
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v1\AugmentStartupActivity.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */