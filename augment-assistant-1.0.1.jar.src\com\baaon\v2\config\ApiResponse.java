/*    */ package com.baaon.v2.config;
/*    */ 
/*    */ public class ApiResponse<T> {
/*    */   private boolean success;
/*    */   private T data;
/*    */   private String message;
/*    */   private String errorCode;
/*    */   
/*    */   public ApiResponse(boolean success, T data, String message) {
/* 10 */     this.success = success;
/* 11 */     this.data = data;
/* 12 */     this.message = message;
/*    */   }
/*    */   
/*    */   public ApiResponse(boolean success, String message, String errorCode) {
/* 16 */     this.success = success;
/* 17 */     this.message = message;
/* 18 */     this.errorCode = errorCode;
/*    */   }
/*    */   
/*    */   public static <T> ApiResponse<T> success(T data) {
/* 22 */     return new ApiResponse<>(true, data, "操作成功");
/*    */   }
/*    */   
/*    */   public static <T> ApiResponse<T> success(T data, String message) {
/* 26 */     return new ApiResponse<>(true, data, message);
/*    */   }
/*    */   
/*    */   public static <T> ApiResponse<T> failure(String message) {
/* 30 */     return new ApiResponse<>(false, message, (String)null);
/*    */   }
/*    */   
/*    */   public static <T> ApiResponse<T> failure(String message, String errorCode) {
/* 34 */     return new ApiResponse<>(false, message, errorCode);
/*    */   }
/*    */   
/*    */   public boolean isSuccess() {
/* 38 */     return this.success;
/*    */   }
/*    */   
/*    */   public void setSuccess(boolean success) {
/* 42 */     this.success = success;
/*    */   }
/*    */   
/*    */   public T getData() {
/* 46 */     return this.data;
/*    */   }
/*    */   
/*    */   public void setData(T data) {
/* 50 */     this.data = data;
/*    */   }
/*    */   
/*    */   public String getMessage() {
/* 54 */     return this.message;
/*    */   }
/*    */   
/*    */   public void setMessage(String message) {
/* 58 */     this.message = message;
/*    */   }
/*    */   
/*    */   public String getErrorCode() {
/* 62 */     return this.errorCode;
/*    */   }
/*    */   
/*    */   public void setErrorCode(String errorCode) {
/* 66 */     this.errorCode = errorCode;
/*    */   }
/*    */   
/*    */   public String toString() {
/* 70 */     boolean var10000 = this.success;
/* 71 */     return "ApiResponse{success=" + var10000 + ", data=" + String.valueOf(this.data) + ", message='" + this.message + "', errorCode='" + this.errorCode + "'}";
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\config\ApiResponse.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */