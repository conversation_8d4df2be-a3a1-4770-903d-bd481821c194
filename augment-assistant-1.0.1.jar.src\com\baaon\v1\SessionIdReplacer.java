/*     */ package com.baaon.v1;
/*     */ 
/*     */ import com.intellij.ide.plugins.IdeaPluginDescriptor;
/*     */ import com.intellij.ide.plugins.PluginManager;
/*     */ import com.intellij.openapi.application.Application;
/*     */ import com.intellij.openapi.application.ApplicationManager;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import java.lang.reflect.Constructor;
/*     */ import java.lang.reflect.Field;
/*     */ import java.lang.reflect.Method;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SessionIdReplacer
/*     */ {
/*  76 */   private static final Logger LOG = Logger.getInstance(SessionIdReplacer.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean a1() {
/*     */     try {
/* 128 */       ClassLoader f1 = Thread.currentThread().getContextClassLoader();
/* 129 */       ClassLoader f2 = null;
/*     */ 
/*     */       
/*     */       try {
/* 133 */         PluginManager pluginManager = PluginManager.getInstance();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 145 */         IdeaPluginDescriptor targetPlugin = null;
/* 146 */         IdeaPluginDescriptor[] allPlugins = PluginManager.getPlugins();
/*     */ 
/*     */         
/* 149 */         for (IdeaPluginDescriptor plugin : allPlugins) {
/* 150 */           if ("com.augmentcode".equals(plugin.getPluginId().getIdString())) {
/* 151 */             targetPlugin = plugin;
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */         
/* 157 */         if (targetPlugin != null) {
/* 158 */           f2 = targetPlugin.getPluginClassLoader();
/* 159 */           LOG.info("成功获取目标插件的类加载器");
/*     */         } 
/* 161 */       } catch (Exception e) {
/* 162 */         LOG.warn("无法获取目标插件类加载器，将使用当前类加载器: " + e.getMessage());
/*     */       } 
/*     */ 
/*     */       
/* 166 */       if (f2 == null) {
/* 167 */         f2 = getClass().getClassLoader();
/* 168 */         LOG.info("使用当前类加载器作为备用方案");
/*     */       } 
/*     */ 
/*     */       
/* 172 */       Thread.currentThread().setContextClassLoader(f2);
/*     */ 
/*     */       
/* 175 */       Class<?> apiImplClass = Class.forName("com.augmentcode.intellij.api.AugmentAPI", true, f2);
/*     */ 
/*     */       
/* 178 */       Application app = ApplicationManager.getApplication();
/* 179 */       Method method = app.getClass().getMethod("getService", new Class[] { Class.class });
/* 180 */       Object augmentApiInstance = method.invoke(app, new Object[] { apiImplClass });
/*     */ 
/*     */       
/* 183 */       Field httpClientField = augmentApiInstance.getClass().getDeclaredField("httpClient");
/* 184 */       httpClientField.setAccessible(true);
/*     */ 
/*     */       
/* 187 */       String sessionId = SessionId.INSTANCE.c1();
/* 188 */       LOG.info("使用配置的SessionId: " + sessionId + " (来源: " + SessionId.INSTANCE.c4() + ")");
/*     */ 
/*     */       
/* 191 */       Class<?> httpClientClass = Class.forName("com.augmentcode.intellij.api.AugmentHttpClient");
/* 192 */       Constructor<?> constructor = httpClientClass.getConstructor(new Class[] { String.class });
/* 193 */       Object newHttpClient = constructor.newInstance(new Object[] { sessionId });
/*     */ 
/*     */       
/* 196 */       httpClientField.set(augmentApiInstance, newHttpClient);
/* 197 */       LOG.info("成功重新初始化httpClient实例");
/*     */ 
/*     */       
/* 200 */       Thread.currentThread().setContextClassLoader(f1);
/* 201 */       return true;
/* 202 */     } catch (Exception e) {
/* 203 */       LOG.error("重新初始化httpClient实例失败", e);
/* 204 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean a2() {
/*     */     try {
/* 261 */       if (a1()) {
/* 262 */         LOG.info("SessionId类替换操作成功完成");
/* 263 */         return true;
/*     */       } 
/* 265 */       LOG.warn("所有替换方法都失败，SessionId未能成功替换");
/* 266 */       return false;
/*     */     }
/* 268 */     catch (Exception e) {
/*     */       
/* 270 */       LOG.error("替换SessionId类时出错", e);
/* 271 */       return false;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v1\SessionIdReplacer.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */