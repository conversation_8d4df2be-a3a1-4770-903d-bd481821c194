/*     */ package com.baaon.v1;
/*     */ 
/*     */ import com.intellij.ide.util.PropertiesComponent;
/*     */ import com.intellij.openapi.application.PermanentInstallationID;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import java.util.UUID;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SessionId
/*     */ {
/*  47 */   private static final Logger LOG = Logger.getInstance(SessionId.class);
/*     */   
/*     */   @NotNull
/*  50 */   public static final SessionId INSTANCE = new SessionId();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   private static final String SESSION_ID_KEY = "augment.session.id";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String c1() {
/*  87 */     LOG.info("获取SessionId");
/*  88 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/*  89 */     String storedSessionID = properties.getValue("augment.session.id");
/*  90 */     if (storedSessionID != null && !isBlank(storedSessionID)) {
/*  91 */       return storedSessionID;
/*     */     }
/*     */     
/*  94 */     String newSessionID = UUID.randomUUID().toString();
/*  95 */     properties.setValue("augment.session.id", newSessionID);
/*  96 */     LOG.info("生成新的SessionId: " + newSessionID);
/*  97 */     return newSessionID;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String c2() {
/* 125 */     String newSessionId = UUID.randomUUID().toString();
/* 126 */     PropertiesComponent.getInstance().setValue("augment.session.id", newSessionId);
/* 127 */     LOG.info("重置SessionId: " + newSessionId);
/* 128 */     return newSessionId;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isBlank(String str) {
/* 142 */     if (str == null) {
/* 143 */       return true;
/*     */     }
/* 145 */     return str.trim().isEmpty();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String c3() {
/* 172 */     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
/* 173 */     if (storedSessionID != null && !isBlank(storedSessionID)) {
/* 174 */       return storedSessionID;
/*     */     }
/*     */     
/* 177 */     String installationID = PermanentInstallationID.get();
/* 178 */     if (installationID != null && !isBlank(installationID)) {
/* 179 */       LOG.info("使用PermanentInstallationID作为SessionId: " + installationID);
/* 180 */       return installationID;
/*     */     } 
/* 182 */     LOG.warn("没有找到有效的SessionId或InstallationID");
/* 183 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasValidSessionId() {
/* 211 */     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
/* 212 */     if (storedSessionID != null && !isBlank(storedSessionID)) {
/* 213 */       return true;
/*     */     }
/* 215 */     String installationID = PermanentInstallationID.get();
/* 216 */     return (installationID != null && !isBlank(installationID));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void clearStoredSessionId() {
/* 239 */     PropertiesComponent.getInstance().unsetValue("augment.session.id");
/* 240 */     LOG.info("已清除存储的SessionId");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String c4() {
/* 266 */     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
/* 267 */     if (storedSessionID != null && !isBlank(storedSessionID)) {
/* 268 */       return "PropertiesComponent";
/*     */     }
/* 270 */     String installationID = PermanentInstallationID.get();
/* 271 */     return (installationID != null && !isBlank(installationID)) ? "PermanentInstallationID" : "Generated";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String c5() {
/* 302 */     String sessionId = c1();
/* 303 */     String source = c4();
/* 304 */     return String.format("SessionID: %s (Source: %s)", new Object[] { sessionId, source });
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v1\SessionId.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */