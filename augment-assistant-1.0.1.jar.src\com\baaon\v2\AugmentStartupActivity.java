/*    */ package com.baaon.v2;
/*    */ 
/*    */ import com.intellij.openapi.diagnostic.Logger;
/*    */ import com.intellij.openapi.project.Project;
/*    */ import com.intellij.openapi.startup.ProjectActivity;
/*    */ import kotlin.Unit;
/*    */ import kotlin.coroutines.Continuation;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AugmentStartupActivity
/*    */   implements ProjectActivity
/*    */ {
/* 17 */   private static final Logger LOG = Logger.getInstance(AugmentStartupActivity.class);
/*    */   
/*    */   @Nullable
/*    */   public Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
/* 21 */     LOG.info("开始替换目标插件类...");
/*    */     
/*    */     try {
/* 24 */       SessionIdReplacer replacer = new SessionIdReplacer();
/* 25 */       if (replacer.replaceSessionIdClass()) {
/* 26 */         LOG.info("成功替换SessionId类");
/*    */       } else {
/* 28 */         LOG.warn("替换SessionId类失败");
/*    */       } 
/* 30 */     } catch (Exception e) {
/* 31 */       LOG.error("替换过程中发生错误", e);
/*    */     } 
/*    */     
/* 34 */     return Unit.INSTANCE;
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\AugmentStartupActivity.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */