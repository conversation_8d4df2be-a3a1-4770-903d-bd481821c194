/*    */ package com.baaon.v2;
/*    */ 
/*    */ import com.intellij.openapi.application.Application;
/*    */ import com.intellij.openapi.application.ApplicationManager;
/*    */ import com.intellij.openapi.diagnostic.Logger;
/*    */ import java.lang.reflect.Field;
/*    */ import java.util.Map;
/*    */ 
/*    */ public class UnsafeCrossPluginAccess
/*    */ {
/* 11 */   private static final Logger LOG = Logger.getInstance(UnsafeCrossPluginAccess.class);
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Object tryGetService(String pluginId, String serviceClassName) {
/*    */     try {
/* 18 */       Application app = ApplicationManager.getApplication();
/* 19 */       Field servicesField = app.getClass().getDeclaredField("myServices");
/* 20 */       servicesField.setAccessible(true);
/* 21 */       Map<?, ?> services = (Map<?, ?>)servicesField.get(app);
/*    */       
/* 23 */       for (Map.Entry<?, ?> entry : services.entrySet()) {
/* 24 */         Object service = entry.getValue();
/* 25 */         String var10001 = (service != null) ? service.getClass().getName() : "none";
/* 26 */         LOG.info("services: " + var10001);
/* 27 */         if (service != null && service.getClass().getName().equals(serviceClassName)) {
/* 28 */           return service;
/*    */         }
/*    */       } 
/* 31 */     } catch (Exception e) {
/* 32 */       LOG.error("获取Service异常");
/* 33 */       e.printStackTrace();
/*    */     } 
/*    */     
/* 36 */     return null;
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\UnsafeCrossPluginAccess.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */