<idea-plugin>
  <idea-version since-build="241" until-build="252.*" />
  <version>1.0.1</version>
  <id>com.baaon.augment_assistant</id>
  <name>Augment Assistant</name>
  <vendor email="<EMAIL>" url="https://aibook.ren">AI全书</vendor>
  <description><![CDATA[<p>Augment Assistant</p>
        <p>主要功能：</p>
        <ul>
            <li>重置SessionId</li>
            <li>自动生成邮箱地址</li>
            <li>自动获取验证码</li>
            <li>学习AI，来AI全书...</li>
        </ul>]]></description>
  <depends>com.intellij.modules.platform</depends>
  <depends>com.augmentcode</depends>
  <extensions defaultExtensionNs="com.intellij">
    <projectActivity implementation="com.baaon.v2.AugmentStartupActivity" />
    <applicationConfigurable parentId="tools" instance="com.baaon.v2.config.AugmentConfigurable" id="com.baaon.v2.config.AugmentConfigurable" displayName="Augment Assistant" />
  </extensions>
  <applicationListeners>
    <listener class="com.baaon.v2.AugmentSessionReplacerPlugin" topic="com.intellij.ide.AppLifecycleListener" />
  </applicationListeners>
</idea-plugin>
