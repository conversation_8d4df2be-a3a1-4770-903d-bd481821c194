/*     */ package com.baaon.v2.config;
/*     */ 
/*     */ import com.baaon.v2.AuthenticationManager;
/*     */ import com.baaon.v2.SessionId;
/*     */ import com.baaon.v2.SessionIdReplacer;
/*     */ import com.baaon.v2.TrialSessionManager;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import com.intellij.openapi.ui.Messages;
/*     */ import com.intellij.ui.components.JBLabel;
/*     */ import com.intellij.ui.components.JBTextField;
/*     */ import com.intellij.util.ui.FormBuilder;
/*     */ import com.intellij.util.ui.JBUI;
/*     */ import java.awt.Color;
/*     */ import java.awt.Component;
/*     */ import java.awt.Desktop;
/*     */ import java.awt.FlowLayout;
/*     */ import java.awt.Font;
/*     */ import java.awt.Toolkit;
/*     */ import java.awt.datatransfer.ClipboardOwner;
/*     */ import java.awt.datatransfer.StringSelection;
/*     */ import java.awt.event.ActionEvent;
/*     */ import java.awt.event.ActionListener;
/*     */ import java.net.URI;
/*     */ import java.util.concurrent.Executors;
/*     */ import java.util.concurrent.ScheduledExecutorService;
/*     */ import java.util.concurrent.TimeUnit;
/*     */ import javax.swing.JButton;
/*     */ import javax.swing.JComponent;
/*     */ import javax.swing.JPanel;
/*     */ import javax.swing.SwingUtilities;
/*     */ import javax.swing.Timer;
/*     */ 
/*     */ public class AugmentConfigPanel {
/*  34 */   private static final Logger LOG = Logger.getInstance(AugmentConfigPanel.class);
/*     */   private JPanel mainPanel;
/*     */   private JBTextField sessionIdField;
/*     */   private JBLabel sessionIdSourceLabel;
/*     */   private JButton trialActivateButton;
/*     */   private JBLabel trialStatusLabel;
/*     */   private JPanel trialPanel;
/*     */   private JBTextField formalCodeField;
/*     */   private JButton formalVerifyButton;
/*     */   private JButton formalHelpButton;
/*     */   private JButton formalTutorialButton;
/*     */   private JBLabel formalStatusLabel;
/*     */   private JButton generateButton;
/*     */   private JButton copyButton;
/*     */   private JButton debugClearButton;
/*     */   private JBTextField emailField;
/*     */   private JButton generateEmailButton;
/*     */   private JButton copyEmailButton;
/*     */   private JButton queryCodeButton;
/*     */   private JButton copyCodeButton;
/*     */   private JBLabel emailStatusLabel;
/*     */   private JBLabel codeStatusLabel;
/*     */   private String originalSessionId;
/*     */   private boolean modified = false;
/*  58 */   private AuthenticationManager authManager = AuthenticationManager.getInstance();
/*  59 */   private TrialSessionManager trialManager = TrialSessionManager.getInstance();
/*  60 */   private EmailVerificationApiService apiService = new EmailVerificationApiService();
/*     */   private ScheduledExecutorService statusUpdateScheduler;
/*     */   private volatile boolean isDisposed = false;
/*  63 */   private long lastQueryTime = 0L;
/*     */   private static final int QUERY_INTERVAL_SECONDS = 5;
/*     */   private Timer queryTimer;
/*     */   
/*     */   public AugmentConfigPanel() {
/*  68 */     initializeComponents();
/*  69 */     setupLayout();
/*  70 */     setupEventHandlers();
/*  71 */     loadAuthenticationStatus();
/*  72 */     loadCurrentSettings();
/*  73 */     startStatusUpdateScheduler();
/*     */   }
/*     */   
/*     */   private void initializeComponents() {
/*  77 */     this.trialActivateButton = new JButton("激活3天试用");
/*  78 */     this.trialActivateButton.setToolTipText("点击激活3天试用期（验证码：1024）");
/*  79 */     this.trialStatusLabel = new JBLabel("点击按钮激活3天试用期");
/*  80 */     this.trialStatusLabel.setFont(this.trialStatusLabel.getFont().deriveFont(2));
/*  81 */     this.trialStatusLabel.setForeground(Color.ORANGE);
/*  82 */     this.formalCodeField = new JBTextField();
/*  83 */     this.formalCodeField.setColumns(15);
/*  84 */     this.formalCodeField.setToolTipText("输入正式验证码，永久解锁功能");
/*  85 */     this.formalVerifyButton = new JButton("正式验证");
/*  86 */     this.formalHelpButton = new JButton("如何获取正式验证码？");
/*  87 */     this.formalHelpButton.setBackground(new Color(76, 175, 80));
/*  88 */     this.formalHelpButton.setForeground(Color.WHITE);
/*  89 */     this.formalHelpButton.setToolTipText("点击查看如何获取正式验证码的详细说明");
/*  90 */     this.formalTutorialButton = new JButton("使用教程");
/*  91 */     this.formalTutorialButton.setBackground(new Color(255, 193, 7));
/*  92 */     this.formalTutorialButton.setForeground(Color.BLACK);
/*  93 */     this.formalTutorialButton.setToolTipText("点击查看详细的使用教程");
/*  94 */     this.formalStatusLabel = new JBLabel("输入正式验证码永久解锁功能");
/*  95 */     this.formalStatusLabel.setFont(this.formalStatusLabel.getFont().deriveFont(2));
/*  96 */     this.formalStatusLabel.setForeground(Color.ORANGE);
/*  97 */     this.sessionIdField = new JBTextField();
/*  98 */     this.sessionIdField.setEditable(false);
/*  99 */     this.sessionIdField.setFont(new Font("Monospaced", 0, 12));
/* 100 */     this.sessionIdSourceLabel = new JBLabel();
/* 101 */     this.sessionIdSourceLabel.setFont(this.sessionIdSourceLabel.getFont().deriveFont(2));
/* 102 */     this.generateButton = new JButton("生成新的SessionId");
/* 103 */     this.copyButton = new JButton("复制到剪贴板");
/* 104 */     this.emailField = new JBTextField();
/* 105 */     this.emailField.setColumns(25);
/* 106 */     this.emailField.setEditable(false);
/* 107 */     this.emailField.setFont(new Font("Monospaced", 0, 12));
/* 108 */     this.emailField.setToolTipText("自动生成的邮箱地址，用于接收验证码");
/* 109 */     this.generateEmailButton = new JButton("生成邮箱");
/* 110 */     this.generateEmailButton.setToolTipText("点击生成一个临时邮箱地址");
/* 111 */     this.copyEmailButton = new JButton("复制邮箱");
/* 112 */     this.copyEmailButton.setToolTipText("复制邮箱地址到剪贴板");
/* 113 */     this.copyEmailButton.setEnabled(false);
/* 114 */     this.queryCodeButton = new JButton("查询验证码");
/* 115 */     this.queryCodeButton.setToolTipText("查询邮箱中最新的验证码（需要约30秒时间，点击间隔30秒）");
/* 116 */     this.queryCodeButton.setEnabled(false);
/* 117 */     this.copyCodeButton = new JButton("复制验证码");
/* 118 */     this.copyCodeButton.setToolTipText("复制验证码到剪贴板");
/* 119 */     this.copyCodeButton.setEnabled(false);
/* 120 */     this.emailStatusLabel = new JBLabel("输入正式验证码后可生成邮箱");
/* 121 */     this.emailStatusLabel.setFont(this.emailStatusLabel.getFont().deriveFont(2));
/* 122 */     this.emailStatusLabel.setForeground(Color.ORANGE);
/* 123 */     this.codeStatusLabel = new JBLabel("生成邮箱后可查询验证码");
/* 124 */     this.codeStatusLabel.setFont(this.codeStatusLabel.getFont().deriveFont(2));
/* 125 */     this.codeStatusLabel.setForeground(Color.GRAY);
/* 126 */     this.debugClearButton = new JButton("清除所有认证状态(调试)");
/* 127 */     this.debugClearButton.setToolTipText("清除所有试用和正式验证状态，仅用于调试");
/* 128 */     this.debugClearButton.setForeground(Color.RED);
/* 129 */     this.debugClearButton.setVisible(false);
/* 130 */     updateButtonStates();
/*     */   }
/*     */   
/*     */   private void setupLayout() {
/* 134 */     this.trialPanel = new JPanel(new FlowLayout(0, 5, 0));
/* 135 */     this.trialPanel.add(this.trialActivateButton);
/* 136 */     JPanel formalPanel = new JPanel(new FlowLayout(0, 5, 0));
/* 137 */     formalPanel.add((Component)this.formalCodeField);
/* 138 */     formalPanel.add(this.formalVerifyButton);
/* 139 */     formalPanel.add(this.formalHelpButton);
/* 140 */     formalPanel.add(this.formalTutorialButton);
/* 141 */     JPanel buttonPanel = new JPanel(new FlowLayout(0, 5, 0));
/* 142 */     buttonPanel.add(this.generateButton);
/* 143 */     buttonPanel.add(this.copyButton);
/* 144 */     JPanel emailPanel = new JPanel(new FlowLayout(0, 5, 0));
/* 145 */     emailPanel.add(this.generateEmailButton);
/* 146 */     emailPanel.add((Component)this.emailField);
/* 147 */     emailPanel.add(this.copyEmailButton);
/* 148 */     JPanel codePanel = new JPanel(new FlowLayout(0, 5, 0));
/* 149 */     codePanel.add(this.queryCodeButton);
/* 150 */     codePanel.add(this.copyCodeButton);
/* 151 */     JPanel debugPanel = new JPanel(new FlowLayout(0, 5, 0));
/* 152 */     debugPanel.add(this.debugClearButton);
/* 153 */     FormBuilder formBuilder = FormBuilder.createFormBuilder();
/* 154 */     formBuilder.addLabeledComponent("试用激活:", this.trialPanel, 1, false).addComponentToRightColumn((JComponent)this.trialStatusLabel, 1).addSeparator().addLabeledComponent("正式验证码:", formalPanel, 1, false).addComponentToRightColumn((JComponent)this.formalStatusLabel, 1).addSeparator();
/* 155 */     formBuilder.addLabeledComponent("当前SessionId:", (JComponent)this.sessionIdField, 1, false).addComponentToRightColumn((JComponent)this.sessionIdSourceLabel, 1).addComponent(buttonPanel, 1).addSeparator().addLabeledComponent("生成邮箱:", emailPanel, 1, false).addComponentToRightColumn((JComponent)this.emailStatusLabel, 1).addLabeledComponent("查询验证码:", codePanel, 1, false).addComponentToRightColumn((JComponent)this.codeStatusLabel, 1).addSeparator().addLabeledComponent("调试功能:", debugPanel, 1, false).addComponentFillVertically(new JPanel(), 0);
/* 156 */     this.mainPanel = formBuilder.getPanel();
/* 157 */     this.mainPanel.setBorder(JBUI.Borders.empty(10));
/* 158 */     updateTrialSectionVisibility();
/*     */   }
/*     */   
/*     */   private void setupEventHandlers() {
/* 162 */     this.trialActivateButton.addActionListener(e -> activateTrialMode());
/* 163 */     this.formalVerifyButton.addActionListener(e -> verifyFormalCode());
/* 164 */     this.formalCodeField.addActionListener(e -> verifyFormalCode());
/* 165 */     this.formalHelpButton.addActionListener(e -> openFormalCodeHelpPage());
/* 166 */     this.formalTutorialButton.addActionListener(e -> openTutorialPage());
/* 167 */     this.generateButton.addActionListener(e -> {
/*     */           if (this.authManager.hasValidAuthentication()) {
/*     */             generateNewSessionId();
/*     */           } else {
/*     */             showAuthenticationRequiredMessage();
/*     */           } 
/*     */         });
/*     */     
/* 175 */     this.copyButton.addActionListener(e -> {
/*     */           if (this.authManager.hasValidAuthentication()) {
/*     */             copySessionIdToClipboard();
/*     */           } else {
/*     */             showAuthenticationRequiredMessage();
/*     */           } 
/*     */         });
/*     */     
/* 183 */     this.generateEmailButton.addActionListener(e -> generateEmail());
/* 184 */     this.copyEmailButton.addActionListener(e -> copyEmailToClipboard());
/* 185 */     this.queryCodeButton.addActionListener(e -> queryVerificationCode());
/* 186 */     this.copyCodeButton.addActionListener(e -> copyCodeToClipboard());
/* 187 */     this.debugClearButton.addActionListener(e -> {
/*     */           int result = Messages.showYesNoDialog("确定要清除所有认证状态吗？\n\n这将清除：\n• 试用验证状态和剩余时间\n• 正式验证状态\n• 所有相关的SessionId数据\n\n此操作不可撤销！", "确认清除认证状态", "确定清除", "取消", Messages.getWarningIcon());
/*     */           if (result == 0) {
/*     */             clearAllAuthenticationStatus();
/*     */             Messages.showInfoMessage("所有认证状态已清除！\n\n现在可以重新激活试用或进行正式验证。", "清除成功");
/*     */           } 
/*     */         });
/*     */   }
/*     */ 
/*     */   
/*     */   private void loadCurrentSettings() {
/*     */     try {
/* 199 */       SessionId sessionIdInstance = SessionId.INSTANCE;
/* 200 */       String currentSessionId = sessionIdInstance.getSessionId();
/* 201 */       String source = sessionIdInstance.getSessionIdSource();
/* 202 */       this.sessionIdField.setText(currentSessionId);
/* 203 */       JBLabel var10000 = this.sessionIdSourceLabel;
/* 204 */       String var10001 = getSourceDescription(source);
/* 205 */       var10000.setText("来源: " + var10001);
/* 206 */       this.originalSessionId = currentSessionId;
/* 207 */       this.modified = false;
/* 208 */       LOG.info("加载当前SessionId配置: " + currentSessionId + " (来源: " + source + ")");
/* 209 */       loadSavedEmailAddress();
/* 210 */     } catch (Exception e) {
/* 211 */       LOG.error("加载SessionId配置失败", e);
/* 212 */       this.sessionIdField.setText("加载失败");
/* 213 */       this.sessionIdSourceLabel.setText("来源: 未知");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private String getSourceDescription(String source) {
/* 219 */     switch (source) {
/*     */       case "TrialSession":
/* 221 */         return "试用会话 (剩余 " + this.trialManager.getRemainingDays() + " 天)";
/*     */       
/*     */       case "PermanentInstallationID":
/* 224 */         return "永久安装ID";
/*     */       
/*     */       case "PropertiesComponent":
/* 227 */         return "已保存的配置";
/*     */       
/*     */       case "Generated":
/* 230 */         return "自动生成";
/*     */     } 
/*     */     
/* 233 */     return source;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void loadSavedEmailAddress() {
/*     */     try {
/* 240 */       String savedEmail = this.authManager.getSavedEmailAddress();
/* 241 */       if (savedEmail != null && !savedEmail.trim().isEmpty()) {
/* 242 */         this.emailField.setText(savedEmail);
/* 243 */         this.emailStatusLabel.setText("已加载保存的邮箱地址");
/* 244 */         this.emailStatusLabel.setForeground(Color.GREEN);
/* 245 */         this.copyEmailButton.setEnabled(true);
/* 246 */         this.queryCodeButton.setEnabled(true);
/* 247 */         this.codeStatusLabel.setText("可以查询验证码");
/* 248 */         this.codeStatusLabel.setForeground(Color.ORANGE);
/* 249 */         LOG.info("已加载保存的邮箱地址: " + savedEmail);
/*     */       } else {
/* 251 */         this.emailField.setText("");
/* 252 */         this.emailStatusLabel.setText("点击生成邮箱按钮获取临时邮箱地址");
/* 253 */         this.emailStatusLabel.setForeground(Color.ORANGE);
/* 254 */         this.copyEmailButton.setEnabled(false);
/* 255 */         this.queryCodeButton.setEnabled(false);
/* 256 */         this.copyCodeButton.setEnabled(false);
/* 257 */         this.codeStatusLabel.setText("生成邮箱后可查询验证码");
/* 258 */         this.codeStatusLabel.setForeground(Color.GRAY);
/* 259 */         LOG.info("未找到保存的邮箱地址，已重置邮箱相关状态");
/*     */       } 
/* 261 */     } catch (Exception e) {
/* 262 */       LOG.error("加载保存的邮箱地址失败", e);
/* 263 */       this.emailStatusLabel.setText("加载邮箱地址失败");
/* 264 */       this.emailStatusLabel.setForeground(Color.RED);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void generateNewSessionId() {
/*     */     try {
/* 271 */       String newSessionId = SessionId.INSTANCE.resetSessionId();
/* 272 */       this.sessionIdField.setText(newSessionId);
/* 273 */       this.sessionIdSourceLabel.setText("来源: " + getSourceDescription("PropertiesComponent"));
/* 274 */       this.modified = true;
/* 275 */       LOG.info("生成新的SessionId: " + newSessionId);
/*     */       
/*     */       try {
/* 278 */         SessionIdReplacer replacer = new SessionIdReplacer();
/* 279 */         boolean success = replacer.replaceSessionIdClass();
/* 280 */         if (success) {
/* 281 */           Messages.showInfoMessage("新的SessionId已生成并立即生效！\n\n" + newSessionId, "SessionId生成成功");
/* 282 */           LOG.info("SessionId替换成功，新SessionId已生效");
/*     */         } else {
/* 284 */           Messages.showWarningDialog("新的SessionId已生成并保存，但替换失败。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。", "SessionId生成成功，但替换失败");
/* 285 */           LOG.warn("SessionId生成成功，但替换失败");
/*     */         } 
/* 287 */       } catch (Exception replaceException) {
/* 288 */         LOG.error("调用SessionIdReplacer失败", replaceException);
/* 289 */         Messages.showWarningDialog("新的SessionId已生成并保存，但替换过程出现异常。\n\nSessionId: " + newSessionId + "\n\n请重启IDE以确保新SessionId生效。\n\n错误详情: " + replaceException.getMessage(), "SessionId生成成功，但替换异常");
/*     */       } 
/* 291 */     } catch (Exception e) {
/* 292 */       LOG.error("生成SessionId失败", e);
/* 293 */       Messages.showErrorDialog("生成SessionId失败: " + e.getMessage(), "错误");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void copySessionIdToClipboard() {
/*     */     try {
/* 300 */       String sessionId = this.sessionIdField.getText();
/* 301 */       if (sessionId != null && !sessionId.trim().isEmpty()) {
/* 302 */         Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(sessionId), (ClipboardOwner)null);
/* 303 */         Messages.showInfoMessage("SessionId已复制到剪贴板！", "复制成功");
/* 304 */         LOG.info("SessionId已复制到剪贴板");
/*     */       } 
/* 306 */     } catch (Exception e) {
/* 307 */       LOG.error("复制SessionId失败", e);
/* 308 */       Messages.showErrorDialog("复制SessionId失败: " + e.getMessage(), "错误");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public JPanel getMainPanel() {
/* 314 */     return this.mainPanel;
/*     */   }
/*     */   
/*     */   public boolean isModified() {
/* 318 */     return this.modified;
/*     */   }
/*     */   
/*     */   public void apply() {
/* 322 */     this.modified = false;
/* 323 */     this.originalSessionId = this.sessionIdField.getText();
/* 324 */     LOG.info("配置已应用");
/*     */   }
/*     */   
/*     */   public void reset() {
/* 328 */     loadCurrentSettings();
/* 329 */     LOG.info("配置已重置");
/*     */   }
/*     */   
/*     */   private void activateTrialMode() {
/* 333 */     this.trialActivateButton.setEnabled(false);
/* 334 */     this.trialActivateButton.setText("激活中...");
/* 335 */     this.trialStatusLabel.setText("正在激活试用模式，请稍候...");
/* 336 */     this.trialStatusLabel.setForeground(Color.BLUE);
/* 337 */     SwingUtilities.invokeLater(() -> (new Thread(())).start());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void verifyFormalCode() {
/* 375 */     String inputCode = this.formalCodeField.getText().trim();
/* 376 */     if (inputCode.isEmpty()) {
/* 377 */       Messages.showWarningDialog("请输入正式验证码！", "验证码为空");
/*     */     } else {
/* 379 */       this.authManager.saveAuthCode(inputCode);
/* 380 */       LOG.info("已保存授权码用于邮箱验证功能: " + inputCode);
/* 381 */       this.formalVerifyButton.setEnabled(false);
/* 382 */       this.formalVerifyButton.setText("验证中...");
/* 383 */       this.formalStatusLabel.setText("正在验证正式验证码，请稍候...");
/* 384 */       this.formalStatusLabel.setForeground(Color.BLUE);
/* 385 */       SwingUtilities.invokeLater(() -> (new Thread(())).start());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void showAuthenticationRequiredMessage() {
/* 423 */     Messages.showWarningDialog("请先进行试用验证或正式验证以启用SessionId功能！", "需要认证");
/*     */   }
/*     */   
/*     */   private void openFormalCodeHelpPage() {
/*     */     try {
/* 428 */       String helpUrl = "https://docs.qq.com/doc/DZWFuaVdtemZnUUZj";
/* 429 */       if (Desktop.isDesktopSupported()) {
/* 430 */         Desktop desktop = Desktop.getDesktop();
/* 431 */         if (desktop.isSupported(Desktop.Action.BROWSE)) {
/* 432 */           desktop.browse(new URI(helpUrl));
/* 433 */           LOG.info("已打开正式验证码帮助页面: " + helpUrl);
/*     */         } else {
/* 435 */           showBrowserNotSupportedMessage(helpUrl);
/*     */         } 
/*     */       } else {
/* 438 */         showBrowserNotSupportedMessage(helpUrl);
/*     */       } 
/* 440 */     } catch (Exception e) {
/* 441 */       LOG.error("打开帮助页面失败", e);
/* 442 */       Messages.showErrorDialog("无法打开帮助页面！\n\n请手动访问以下链接获取正式验证码：\nhttps://docs.qq.com/doc/DZWFuaVdtemZnUUZj\n\n错误详情: " + e.getMessage(), "打开失败");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void openTutorialPage() {
/*     */     try {
/* 449 */       String tutorialUrl = "https://docs.qq.com/doc/DZUtOVm1DZW1kQXFm";
/* 450 */       if (Desktop.isDesktopSupported()) {
/* 451 */         Desktop desktop = Desktop.getDesktop();
/* 452 */         if (desktop.isSupported(Desktop.Action.BROWSE)) {
/* 453 */           desktop.browse(new URI(tutorialUrl));
/* 454 */           LOG.info("已打开使用教程页面: " + tutorialUrl);
/*     */         } else {
/* 456 */           showBrowserNotSupportedMessage(tutorialUrl);
/*     */         } 
/*     */       } else {
/* 459 */         showBrowserNotSupportedMessage(tutorialUrl);
/*     */       } 
/* 461 */     } catch (Exception e) {
/* 462 */       LOG.error("打开教程页面失败", e);
/* 463 */       Messages.showErrorDialog("无法打开教程页面！\n\n请手动访问以下链接查看使用教程：\nhttps://docs.qq.com/doc/DZUtOVm1DZW1kQXFm\n\n错误详情: " + e.getMessage(), "打开失败");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void showBrowserNotSupportedMessage(String url) {
/* 469 */     Messages.showInfoMessage("系统不支持自动打开浏览器！\n\n请手动复制以下链接到浏览器中访问：\n" + url, "手动访问");
/* 470 */     LOG.warn("系统不支持自动打开浏览器，需要手动访问: " + url);
/*     */   }
/*     */   
/*     */   private void updateTrialSectionVisibility() {
/* 474 */     boolean isFormallyVerified = this.authManager.isFormallyVerified();
/* 475 */     if (this.trialPanel != null) {
/* 476 */       this.trialPanel.setVisible(!isFormallyVerified);
/* 477 */       this.trialStatusLabel.setVisible(!isFormallyVerified);
/*     */     } 
/*     */     
/* 480 */     LOG.info("试用部分可见性已更新: " + (!isFormallyVerified ? "显示" : "隐藏"));
/*     */   }
/*     */   
/*     */   private void updateButtonStates() {
/* 484 */     boolean hasAuth = this.authManager.hasValidAuthentication();
/* 485 */     this.generateButton.setEnabled(hasAuth);
/* 486 */     this.copyButton.setEnabled(hasAuth);
/* 487 */     if (hasAuth) {
/* 488 */       this.generateButton.setToolTipText("生成新的SessionId");
/* 489 */       this.copyButton.setToolTipText("复制SessionId到剪贴板");
/*     */     } else {
/* 491 */       this.generateButton.setToolTipText("请先进行试用或正式验证");
/* 492 */       this.copyButton.setToolTipText("请先进行试用或正式验证");
/*     */     } 
/*     */     
/* 495 */     boolean isFormallyVerified = this.authManager.isFormallyVerified();
/* 496 */     boolean hasTrialCode = this.trialManager.hasTrialCode();
/* 497 */     this.trialActivateButton.setEnabled((!isFormallyVerified && !hasTrialCode));
/* 498 */     this.formalVerifyButton.setEnabled(!isFormallyVerified);
/* 499 */     this.formalCodeField.setEnabled(!isFormallyVerified);
/* 500 */     this.formalHelpButton.setEnabled(true);
/* 501 */     this.formalTutorialButton.setEnabled(true);
/*     */   }
/*     */   
/*     */   private void loadAuthenticationStatus() {
/* 505 */     boolean isFormallyVerified = this.authManager.isFormallyVerified();
/* 506 */     boolean hasValidTrial = this.trialManager.hasValidTrialSession();
/* 507 */     boolean hasTrialCode = this.trialManager.hasTrialCode();
/* 508 */     if (isFormallyVerified) {
/* 509 */       this.formalStatusLabel.setText("已正式验证！功能已永久解锁");
/* 510 */       this.formalStatusLabel.setForeground(Color.GREEN);
/* 511 */       this.formalCodeField.setText("已验证");
/* 512 */       this.formalCodeField.setEnabled(false);
/* 513 */       this.formalVerifyButton.setEnabled(false);
/* 514 */       this.formalVerifyButton.setText("已验证");
/* 515 */       this.formalHelpButton.setToolTipText("查看正式验证码获取说明（已验证）");
/* 516 */       this.formalTutorialButton.setToolTipText("查看详细的使用教程（已验证）");
/* 517 */       this.trialActivateButton.setEnabled(false);
/* 518 */       this.trialActivateButton.setText("已正式验证");
/* 519 */       this.trialStatusLabel.setText("已正式验证，试用功能已禁用");
/* 520 */       this.trialStatusLabel.setForeground(Color.GRAY);
/* 521 */       LOG.info("加载已保存的认证状态：已正式验证");
/* 522 */     } else if (hasValidTrial) {
/* 523 */       int remainingDays = this.trialManager.getRemainingDays();
/* 524 */       this.trialStatusLabel.setText("试用已激活，剩余 " + remainingDays + " 天");
/* 525 */       this.trialStatusLabel.setForeground(Color.GREEN);
/* 526 */       this.trialActivateButton.setEnabled(false);
/* 527 */       this.trialActivateButton.setText("已激活");
/* 528 */       this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
/* 529 */       this.formalStatusLabel.setForeground(Color.ORANGE);
/* 530 */       LOG.info("加载已保存的认证状态：试用期内，剩余 " + remainingDays + " 天");
/* 531 */     } else if (hasTrialCode) {
/* 532 */       this.trialStatusLabel.setText("试用期已过期，请使用正式验证码");
/* 533 */       this.trialStatusLabel.setForeground(Color.RED);
/* 534 */       this.trialActivateButton.setEnabled(false);
/* 535 */       this.trialActivateButton.setText("已过期");
/* 536 */       this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
/* 537 */       this.formalStatusLabel.setForeground(Color.ORANGE);
/* 538 */       LOG.info("加载已保存的认证状态：试用期已过期");
/*     */     } else {
/* 540 */       this.trialStatusLabel.setText("点击按钮激活3天试用期");
/* 541 */       this.trialStatusLabel.setForeground(Color.ORANGE);
/* 542 */       this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
/* 543 */       this.formalStatusLabel.setForeground(Color.ORANGE);
/* 544 */       LOG.info("加载已保存的认证状态：未认证");
/*     */     } 
/*     */     
/* 547 */     updateButtonStates();
/* 548 */     updateTrialSectionVisibility();
/*     */   }
/*     */   
/*     */   private void refreshUIAfterAuthentication() {
/* 552 */     updateTrialSectionVisibility();
/* 553 */     updateButtonStates();
/* 554 */     loadCurrentSettings();
/* 555 */     this.mainPanel.revalidate();
/* 556 */     this.mainPanel.repaint();
/* 557 */     LOG.info("认证成功后UI状态已刷新");
/*     */   }
/*     */   
/*     */   public void clearAllAuthenticationStatus() {
/* 561 */     this.authManager.clearFormalVerificationStatus();
/* 562 */     this.authManager.clearSavedAuthCode();
/* 563 */     this.trialManager.clearTrialData();
/* 564 */     this.trialActivateButton.setEnabled(true);
/* 565 */     this.trialActivateButton.setText("激活3天试用");
/* 566 */     this.trialStatusLabel.setText("点击按钮激活3天试用期");
/* 567 */     this.trialStatusLabel.setForeground(Color.ORANGE);
/* 568 */     this.formalCodeField.setText("");
/* 569 */     this.formalCodeField.setEnabled(true);
/* 570 */     this.formalVerifyButton.setEnabled(true);
/* 571 */     this.formalVerifyButton.setText("正式验证");
/* 572 */     this.formalHelpButton.setToolTipText("点击查看如何获取正式验证码的详细说明");
/* 573 */     this.formalTutorialButton.setToolTipText("点击查看详细的使用教程");
/* 574 */     this.formalStatusLabel.setText("输入正式验证码永久解锁功能");
/* 575 */     this.formalStatusLabel.setForeground(Color.ORANGE);
/* 576 */     updateButtonStates();
/* 577 */     loadCurrentSettings();
/* 578 */     updateTrialSectionVisibility();
/* 579 */     this.mainPanel.revalidate();
/* 580 */     this.mainPanel.repaint();
/* 581 */     LOG.info("已清除所有认证状态");
/*     */   }
/*     */   
/*     */   private void startStatusUpdateScheduler() {
/* 585 */     if (this.statusUpdateScheduler == null || this.statusUpdateScheduler.isShutdown()) {
/* 586 */       this.statusUpdateScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
/*     */             Thread thread = new Thread(r, "AugmentConfigPanel-StatusUpdater");
/*     */             thread.setDaemon(true);
/*     */             return thread;
/*     */           });
/* 591 */       this.statusUpdateScheduler.scheduleWithFixedDelay(() -> {
/*     */ 
/*     */ 
/*     */             
/*     */             if (!this.isDisposed) {
/*     */               
/*     */               try {
/*     */                 
/*     */                 SwingUtilities.invokeLater(());
/* 600 */               } catch (Exception e) {
/*     */                 LOG.warn("后台状态更新失败", e);
/*     */               } 
/*     */             }
/*     */           }1L, 1L, TimeUnit.HOURS);
/*     */       
/* 606 */       LOG.info("后台状态更新调度器已启动，每1小时检查一次");
/*     */     } 
/*     */   }
/*     */   
/*     */   private void updateAuthenticationStatusSilently() {
/*     */     try {
/* 612 */       boolean previousAuthState = this.generateButton.isEnabled();
/* 613 */       boolean isFormallyVerified = this.authManager.isFormallyVerified();
/* 614 */       boolean hasValidTrial = this.trialManager.hasValidTrialSession();
/* 615 */       boolean hasTrialCode = this.trialManager.hasTrialCode();
/* 616 */       boolean currentAuthState = this.authManager.hasValidAuthentication();
/* 617 */       if (previousAuthState != currentAuthState) {
/* 618 */         LOG.info("检测到认证状态变化: " + previousAuthState + " -> " + currentAuthState);
/* 619 */         updateButtonStates();
/* 620 */         loadCurrentSettings();
/* 621 */         if (!isFormallyVerified) {
/* 622 */           if (hasValidTrial) {
/* 623 */             int remainingDays = this.trialManager.getRemainingDays();
/* 624 */             this.trialStatusLabel.setText("试用已激活，剩余 " + remainingDays + " 天");
/* 625 */             this.trialStatusLabel.setForeground(Color.GREEN);
/* 626 */           } else if (hasTrialCode && !this.trialStatusLabel.getText().contains("已过期")) {
/* 627 */             this.trialStatusLabel.setText("试用期已过期，请使用正式验证码");
/* 628 */             this.trialStatusLabel.setForeground(Color.RED);
/* 629 */             this.trialActivateButton.setEnabled(false);
/* 630 */             this.trialActivateButton.setText("已过期");
/* 631 */             LOG.info("试用期已过期，UI状态已更新");
/*     */           } 
/*     */         }
/*     */       } 
/* 635 */     } catch (Exception e) {
/* 636 */       LOG.warn("静默更新认证状态失败", e);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void generateEmail() {
/* 642 */     String authCode = this.authManager.getSavedAuthCode();
/* 643 */     if (authCode != null && !authCode.isEmpty()) {
/* 644 */       this.generateEmailButton.setEnabled(false);
/* 645 */       this.generateEmailButton.setText("生成中...");
/* 646 */       this.emailStatusLabel.setText("正在生成邮箱地址，请稍候...");
/* 647 */       this.emailStatusLabel.setForeground(Color.BLUE);
/* 648 */       (new Thread(() -> {
/*     */             ApiResponse<String> response = this.apiService.generateEmail(authCode);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*     */             SwingUtilities.invokeLater(());
/* 673 */           })).start();
/*     */     } else {
/* 675 */       Messages.showWarningDialog("请先输入正式验证码！\n\n授权码将自动保存并用于邮箱验证功能。", "未找到授权码");
/*     */     } 
/*     */   }
/*     */   
/*     */   private void copyEmailToClipboard() {
/*     */     try {
/* 681 */       String email = this.emailField.getText();
/* 682 */       if (email != null && !email.trim().isEmpty()) {
/* 683 */         Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(email), (ClipboardOwner)null);
/* 684 */         Messages.showInfoMessage("邮箱地址已复制到剪贴板！", "复制成功");
/* 685 */         LOG.info("邮箱地址已复制到剪贴板: " + email);
/*     */       } 
/* 687 */     } catch (Exception e) {
/* 688 */       LOG.error("复制邮箱地址失败", e);
/* 689 */       Messages.showErrorDialog("复制邮箱地址失败: " + e.getMessage(), "错误");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void queryVerificationCode() {
/* 695 */     String email = this.emailField.getText().trim();
/* 696 */     String authCode = this.authManager.getSavedAuthCode();
/* 697 */     if (email.isEmpty()) {
/* 698 */       Messages.showWarningDialog("请先生成邮箱地址！", "邮箱地址为空");
/* 699 */     } else if (authCode != null && !authCode.isEmpty()) {
/* 700 */       long currentTime = System.currentTimeMillis();
/* 701 */       long timeSinceLastQuery = (currentTime - this.lastQueryTime) / 1000L;
/* 702 */       if (this.lastQueryTime > 0L && timeSinceLastQuery < 5L) {
/* 703 */         long remainingSeconds = 5L - timeSinceLastQuery;
/* 704 */         Messages.showWarningDialog("请等待 " + remainingSeconds + " 秒后再次查询验证码！\n\n为了避免频繁请求，查询验证码需要间隔 5 秒。", "查询间隔限制");
/*     */       } else {
/* 706 */         this.lastQueryTime = currentTime;
/* 707 */         if (this.queryTimer != null) {
/* 708 */           this.queryTimer.stop();
/*     */         }
/*     */         
/* 711 */         this.queryCodeButton.setEnabled(false);
/* 712 */         this.queryCodeButton.setText("查询中...");
/* 713 */         this.codeStatusLabel.setText("正在查询验证码，大约需要30秒时间，请耐心等待...");
/* 714 */         this.codeStatusLabel.setForeground(Color.BLUE);
/* 715 */         startQueryCountdown();
/* 716 */         (new Thread(() -> {
/*     */               ApiResponse<String> response = this.apiService.queryVerificationCode(email, authCode);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/*     */               SwingUtilities.invokeLater(());
/* 746 */             })).start();
/*     */       } 
/*     */     } else {
/* 749 */       Messages.showWarningDialog("请先输入正式验证码！\n\n授权码将自动保存并用于邮箱验证功能。", "未找到授权码");
/*     */     } 
/*     */   }
/*     */   
/*     */   private void startQueryCountdown() {
/* 754 */     final int[] countdown = { 5 };
/* 755 */     this.queryTimer = new Timer(1000, new ActionListener() {
/*     */           public void actionPerformed(ActionEvent e) {
/* 757 */             countdown[0] = countdown[0] - 1;
/* 758 */             if (countdown[0] > 0) {
/* 759 */               AugmentConfigPanel.this.codeStatusLabel.setText("正在查询验证码，预计还需 " + countdown[0] + " 秒...");
/*     */             } else {
/* 761 */               AugmentConfigPanel.this.codeStatusLabel.setText("查询中，请稍候...");
/* 762 */               AugmentConfigPanel.this.queryTimer.stop();
/*     */             } 
/*     */           }
/*     */         });
/*     */     
/* 767 */     this.queryTimer.start();
/*     */   }
/*     */   
/*     */   private void startIntervalCountdown() {
/* 771 */     final int[] countdown = { 5 };
/* 772 */     this.queryTimer = new Timer(1000, new ActionListener() {
/*     */           public void actionPerformed(ActionEvent e) {
/* 774 */             countdown[0] = countdown[0] - 1;
/* 775 */             if (countdown[0] > 0) {
/* 776 */               AugmentConfigPanel.this.queryCodeButton.setText("等待 " + countdown[0] + " 秒");
/* 777 */               AugmentConfigPanel.this.queryCodeButton.setEnabled(false);
/*     */             } else {
/* 779 */               AugmentConfigPanel.this.queryCodeButton.setText("查询验证码");
/* 780 */               AugmentConfigPanel.this.queryCodeButton.setEnabled(true);
/* 781 */               AugmentConfigPanel.this.queryTimer.stop();
/*     */             } 
/*     */           }
/*     */         });
/*     */     
/* 786 */     this.queryTimer.start();
/*     */   }
/*     */   
/*     */   private void copyCodeToClipboard() {
/*     */     try {
/* 791 */       String codeText = this.codeStatusLabel.getText();
/* 792 */       if (codeText.contains("验证码查询成功: ")) {
/* 793 */         String code = codeText.substring("验证码查询成功: ".length());
/* 794 */         Toolkit.getDefaultToolkit().getSystemClipboard().setContents(new StringSelection(code), (ClipboardOwner)null);
/* 795 */         Messages.showInfoMessage("验证码已复制到剪贴板！\n\n" + code, "复制成功");
/* 796 */         LOG.info("验证码已复制到剪贴板: " + code);
/*     */       } else {
/* 798 */         Messages.showWarningDialog("没有可复制的验证码！\n\n请先查询验证码。", "无验证码");
/*     */       } 
/* 800 */     } catch (Exception e) {
/* 801 */       LOG.error("复制验证码失败", e);
/* 802 */       Messages.showErrorDialog("复制验证码失败: " + e.getMessage(), "错误");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void dispose() {
/* 808 */     this.isDisposed = true;
/* 809 */     if (this.queryTimer != null) {
/* 810 */       this.queryTimer.stop();
/* 811 */       LOG.info("查询验证码定时器已停止");
/*     */     } 
/*     */     
/* 814 */     if (this.statusUpdateScheduler != null && !this.statusUpdateScheduler.isShutdown()) {
/* 815 */       this.statusUpdateScheduler.shutdown();
/*     */       
/*     */       try {
/* 818 */         if (!this.statusUpdateScheduler.awaitTermination(5L, TimeUnit.SECONDS)) {
/* 819 */           this.statusUpdateScheduler.shutdownNow();
/*     */         }
/*     */         
/* 822 */         LOG.info("后台状态更新调度器已停止");
/* 823 */       } catch (InterruptedException e) {
/* 824 */         this.statusUpdateScheduler.shutdownNow();
/* 825 */         Thread.currentThread().interrupt();
/* 826 */         LOG.warn("停止后台状态更新调度器时被中断", e);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\config\AugmentConfigPanel.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */