/*    */ package com.baaon.v2.config;
/*    */ 
/*    */ import com.intellij.openapi.options.Configurable;
/*    */ import com.intellij.openapi.options.ConfigurationException;
/*    */ import com.intellij.openapi.util.NlsContexts.ConfigurableName;
/*    */ import javax.swing.JComponent;
/*    */ import org.jetbrains.annotations.Nls;
/*    */ import org.jetbrains.annotations.Nullable;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AugmentConfigurable
/*    */   implements Configurable
/*    */ {
/*    */   private AugmentConfigPanel configPanel;
/*    */   
/*    */   @Nls(capitalization = Nls.Capitalization.Title)
/*    */   @ConfigurableName
/*    */   public String getDisplayName() {
/* 22 */     return "Augment Assistant";
/*    */   }
/*    */   @Nullable
/*    */   public JComponent createComponent() {
/* 26 */     if (this.configPanel == null) {
/* 27 */       this.configPanel = new AugmentConfigPanel();
/*    */     }
/*    */     
/* 30 */     return this.configPanel.getMainPanel();
/*    */   }
/*    */   
/*    */   public boolean isModified() {
/* 34 */     return (this.configPanel != null && this.configPanel.isModified());
/*    */   }
/*    */   
/*    */   public void apply() throws ConfigurationException {
/* 38 */     if (this.configPanel != null) {
/* 39 */       this.configPanel.apply();
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public void reset() {
/* 45 */     if (this.configPanel != null) {
/* 46 */       this.configPanel.reset();
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public void disposeUIResources() {
/* 52 */     if (this.configPanel != null) {
/* 53 */       this.configPanel.dispose();
/* 54 */       this.configPanel = null;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\config\AugmentConfigurable.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */