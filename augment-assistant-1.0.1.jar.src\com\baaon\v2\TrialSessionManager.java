/*     */ package com.baaon.v2;
/*     */ 
/*     */ import com.intellij.ide.util.PropertiesComponent;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import java.time.Instant;
/*     */ import java.time.temporal.ChronoUnit;
/*     */ import java.util.UUID;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ import org.jetbrains.annotations.Nullable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TrialSessionManager
/*     */ {
/*  17 */   private static final Logger LOG = Logger.getInstance(TrialSessionManager.class);
/*     */   private static final String TRIAL_CODE_KEY = "augment.trial.code";
/*     */   private static final String TRIAL_START_TIME_KEY = "augment.trial.start.time";
/*     */   private static final String TRIAL_SESSION_ID_KEY = "augment.trial.session.id";
/*     */   private static final String ORIGINAL_SESSION_ID_KEY = "augment.original.session.id";
/*     */   private static final int TRIAL_DAYS = 3;
/*  23 */   private static final TrialSessionManager INSTANCE = new TrialSessionManager();
/*     */ 
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public static TrialSessionManager getInstance() {
/*  29 */     return INSTANCE;
/*     */   }
/*     */   
/*     */   public boolean activateTrialCode(String trialCode) {
/*  33 */     if (trialCode == null) return false;
/*     */     
/*  35 */     if (trialCode.trim().isEmpty()) {
/*  36 */       return false;
/*     */     }
/*  38 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/*  39 */     String existingCode = properties.getValue("augment.trial.code");
/*  40 */     if (existingCode != null && !existingCode.trim().isEmpty()) {
/*  41 */       LOG.info("试用验证码已存在，无需重复激活");
/*  42 */       return true;
/*     */     } 
/*  44 */     String currentSessionId = SessionId.INSTANCE.getSessionId();
/*  45 */     properties.setValue("augment.original.session.id", currentSessionId);
/*  46 */     String trialSessionId = UUID.randomUUID().toString();
/*  47 */     properties.setValue("augment.trial.session.id", trialSessionId);
/*  48 */     properties.setValue("augment.trial.code", trialCode);
/*  49 */     properties.setValue("augment.trial.start.time", String.valueOf(Instant.now().toEpochMilli()));
/*  50 */     LOG.info("试用验证码激活成功: " + trialCode + ", 试用SessionId: " + trialSessionId);
/*  51 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasValidTrialSession() {
/*  57 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/*  58 */     String trialCode = properties.getValue("augment.trial.code");
/*  59 */     String startTimeStr = properties.getValue("augment.trial.start.time");
/*  60 */     if (trialCode != null && !trialCode.trim().isEmpty() && startTimeStr != null) {
/*     */       try {
/*  62 */         long startTime = Long.parseLong(startTimeStr);
/*  63 */         Instant startInstant = Instant.ofEpochMilli(startTime);
/*  64 */         Instant now = Instant.now();
/*  65 */         long daysPassed = ChronoUnit.DAYS.between(startInstant, now);
/*  66 */         boolean isValid = (daysPassed < 3L);
/*  67 */         if (!isValid) {
/*  68 */           LOG.info("试用期已过期，已过去 " + daysPassed + " 天");
/*  69 */           clearTrialData();
/*     */         } 
/*     */         
/*  72 */         return isValid;
/*  73 */       } catch (NumberFormatException e) {
/*  74 */         LOG.error("解析试用开始时间失败", e);
/*  75 */         return false;
/*     */       } 
/*     */     }
/*  78 */     return false;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public String getTrialSessionId() {
/*  83 */     if (!hasValidTrialSession()) {
/*  84 */       return null;
/*     */     }
/*  86 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/*  87 */     return properties.getValue("augment.trial.session.id");
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public String getOriginalSessionId() {
/*  92 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/*  93 */     return properties.getValue("augment.original.session.id");
/*     */   }
/*     */   @Nullable
/*     */   public String getTrialCode() {
/*  97 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/*  98 */     return properties.getValue("augment.trial.code");
/*     */   }
/*     */   
/*     */   public int getRemainingDays() {
/* 102 */     if (!hasValidTrialSession()) {
/* 103 */       return 0;
/*     */     }
/* 105 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 106 */     String startTimeStr = properties.getValue("augment.trial.start.time");
/* 107 */     if (startTimeStr == null) {
/* 108 */       return 0;
/*     */     }
/*     */     try {
/* 111 */       long startTime = Long.parseLong(startTimeStr);
/* 112 */       Instant startInstant = Instant.ofEpochMilli(startTime);
/* 113 */       Instant now = Instant.now();
/* 114 */       long daysPassed = ChronoUnit.DAYS.between(startInstant, now);
/* 115 */       return Math.max(0, 3 - (int)daysPassed);
/* 116 */     } catch (NumberFormatException e) {
/* 117 */       LOG.error("解析试用开始时间失败", e);
/* 118 */       return 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void clearTrialData() {
/* 125 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 126 */     properties.unsetValue("augment.trial.code");
/* 127 */     properties.unsetValue("augment.trial.start.time");
/* 128 */     properties.unsetValue("augment.trial.session.id");
/* 129 */     properties.unsetValue("augment.original.session.id");
/* 130 */     LOG.info("试用数据已清理");
/*     */   }
/*     */   
/*     */   public boolean hasTrialCode() {
/* 134 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/* 135 */     String trialCode = properties.getValue("augment.trial.code");
/* 136 */     return (trialCode != null && !trialCode.trim().isEmpty());
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\TrialSessionManager.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */