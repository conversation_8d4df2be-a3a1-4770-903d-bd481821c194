/*    */ package com.baaon.v2;
/*    */ 
/*    */ import com.intellij.ide.plugins.IdeaPluginDescriptor;
/*    */ import com.intellij.ide.plugins.PluginManager;
/*    */ import com.intellij.openapi.application.Application;
/*    */ import com.intellij.openapi.application.ApplicationManager;
/*    */ import com.intellij.openapi.diagnostic.Logger;
/*    */ import java.lang.reflect.Constructor;
/*    */ import java.lang.reflect.Field;
/*    */ import java.lang.reflect.Method;
/*    */ 
/*    */ 
/*    */ public class SessionIdReplacer
/*    */ {
/* 15 */   private static final Logger LOG = Logger.getInstance(SessionIdReplacer.class);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private boolean reinitializeHttpClient() {
/*    */     try {
/* 23 */       ClassLoader originalClassLoader = Thread.currentThread().getContextClassLoader();
/* 24 */       ClassLoader targetClassLoader = null;
/*    */ 
/*    */       
/*    */       try {
/* 28 */         PluginManager pluginManager = PluginManager.getInstance();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */         
/* 36 */         IdeaPluginDescriptor targetPlugin = null;
/* 37 */         IdeaPluginDescriptor[] allPlugins = PluginManager.getPlugins();
/*    */ 
/*    */         
/* 40 */         for (IdeaPluginDescriptor plugin : allPlugins) {
/* 41 */           if ("com.augmentcode".equals(plugin.getPluginId().getIdString())) {
/* 42 */             targetPlugin = plugin;
/*    */             
/*    */             break;
/*    */           } 
/*    */         } 
/*    */         
/* 48 */         if (targetPlugin != null) {
/* 49 */           targetClassLoader = targetPlugin.getPluginClassLoader();
/* 50 */           LOG.info("成功获取目标插件的类加载器");
/*    */         }
/*    */       
/* 53 */       } catch (Exception e) {
/* 54 */         LOG.warn("无法获取目标插件类加载器，将使用当前类加载器: " + e.getMessage());
/*    */       } 
/*    */       
/* 57 */       if (targetClassLoader == null) {
/* 58 */         targetClassLoader = getClass().getClassLoader();
/*    */       }
/*    */       
/* 61 */       Thread.currentThread().setContextClassLoader(targetClassLoader);
/* 62 */       Class<?> apiImplClass = Class.forName("com.augmentcode.intellij.api.AugmentAPI", true, targetClassLoader);
/* 63 */       Application app = ApplicationManager.getApplication();
/* 64 */       Method method = app.getClass().getMethod("getService", new Class[] { Class.class });
/* 65 */       Object invoke = method.invoke(app, new Object[] { apiImplClass });
/* 66 */       Field httpClientField = invoke.getClass().getDeclaredField("httpClient");
/* 67 */       httpClientField.setAccessible(true);
/* 68 */       String sessionId = SessionId.INSTANCE.getSessionId();
/* 69 */       LOG.info("使用配置的SessionId: " + sessionId + " (来源: " + SessionId.INSTANCE.getSessionIdSource() + ")");
/* 70 */       Class<?> httpClientClass = Class.forName("com.augmentcode.intellij.api.AugmentHttpClient");
/* 71 */       Constructor<?> constructor = httpClientClass.getConstructor(new Class[] { String.class });
/* 72 */       Object newHttpClient = constructor.newInstance(new Object[] { sessionId });
/* 73 */       httpClientField.set(invoke, newHttpClient);
/* 74 */       LOG.info("成功重新初始化httpClient实例");
/* 75 */       Thread.currentThread().setContextClassLoader(originalClassLoader);
/* 76 */       return true;
/* 77 */     } catch (Exception e) {
/* 78 */       LOG.error("重新初始化httpClient实例失败", e);
/* 79 */       return false;
/*    */     } 
/*    */   }
/*    */   
/*    */   public boolean replaceSessionIdClass() {
/*    */     try {
/* 85 */       if (reinitializeHttpClient()) {
/* 86 */         return true;
/*    */       }
/* 88 */       LOG.warn("所有替换方法都失败");
/* 89 */       return false;
/*    */     }
/* 91 */     catch (Exception e) {
/* 92 */       LOG.error("替换SessionId类时出错", e);
/* 93 */       return false;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\SessionIdReplacer.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */