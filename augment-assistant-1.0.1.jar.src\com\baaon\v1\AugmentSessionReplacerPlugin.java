/*     */ package com.baaon.v1;
/*     */ 
/*     */ import com.baaon.shaded.org.json.JSONObject;
/*     */ import com.intellij.ide.AppLifecycleListener;
/*     */ import com.intellij.openapi.application.ApplicationManager;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import java.util.List;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AugmentSessionReplacerPlugin
/*     */   implements AppLifecycleListener
/*     */ {
/*  41 */   private static final Logger LOG = Logger.getInstance(AugmentSessionReplacerPlugin.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static volatile boolean isInitialized = false;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public AugmentSessionReplacerPlugin() {
/*  56 */     initialize();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void appFrameCreated(@NotNull List<String> commandLineArgs) {
/*  69 */     if (commandLineArgs == null) {
/*  70 */       throw new IllegalArgumentException("commandLineArgs cannot be null");
/*     */     }
/*     */     
/*  73 */     initialize();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void appStarted() {
/*  83 */     initialize();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void initialize() {
/* 108 */     if (!isInitialized) {
/*     */       try {
/* 110 */         LOG.info("Starting Augment Session ID Replacer Plugin...");
/*     */ 
/*     */         
/* 113 */         ApplicationManager.getApplication().executeOnPooledThread(() -> {
/*     */               try {
/*     */                 SessionIdReplacer replacer = new SessionIdReplacer();
/*     */                 
/*     */                 if (replacer.a2()) {
/*     */                   LOG.info("Successfully replaced SessionId class");
/*     */                   isInitialized = true;
/*     */                 } else {
/*     */                   LOG.warn("Failed to replace SessionId class");
/*     */                 } 
/* 123 */               } catch (Exception e) {
/*     */                 LOG.error("Error during SessionId class replacement", e);
/*     */               } 
/*     */             });
/* 127 */       } catch (Exception e) {
/* 128 */         LOG.error("Failed to initialize Augment Session ID Replacer", e);
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static AugmentSessionReplacerPlugin getInstance() {
/* 150 */     return (AugmentSessionReplacerPlugin)ApplicationManager.getApplication().getService(AugmentSessionReplacerPlugin.class);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject processJsonData(String jsonString) {
/*     */     try {
/* 177 */       JSONObject jsonObject = new JSONObject(jsonString);
/*     */ 
/*     */       
/* 180 */       jsonObject.put("timestamp", System.currentTimeMillis());
/* 181 */       jsonObject.put("plugin", "augment-assistant");
/* 182 */       jsonObject.put("version", "1.0.0");
/*     */       
/* 184 */       LOG.info("Successfully processed JSON data: " + jsonObject.toString());
/* 185 */       return jsonObject;
/* 186 */     } catch (Exception e) {
/* 187 */       LOG.error("Failed to process JSON data", e);
/*     */       
/* 189 */       return new JSONObject();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v1\AugmentSessionReplacerPlugin.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */