/*     */ package com.baaon.v2;
/*     */ 
/*     */ import com.intellij.ide.util.PropertiesComponent;
/*     */ import com.intellij.openapi.application.PermanentInstallationID;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import java.util.UUID;
/*     */ import org.jetbrains.annotations.NotNull;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SessionId
/*     */ {
/*  15 */   private static final Logger LOG = Logger.getInstance(SessionId.class); @NotNull
/*  16 */   public static final SessionId INSTANCE = new SessionId();
/*     */   
/*     */   @NotNull
/*     */   private static final String SESSION_ID_KEY = "augment.session.id";
/*     */   
/*     */   @NotNull
/*     */   public String getSessionId() {
/*  23 */     LOG.info("获取SessionId");
/*  24 */     TrialSessionManager trialManager = TrialSessionManager.getInstance();
/*  25 */     if (trialManager.hasValidTrialSession()) {
/*  26 */       String trialSessionId = trialManager.getTrialSessionId();
/*  27 */       if (trialSessionId != null && !isBlank(trialSessionId)) {
/*  28 */         LOG.info("使用试用SessionId: " + trialSessionId);
/*  29 */         return trialSessionId;
/*     */       } 
/*     */     } 
/*     */     
/*  33 */     PropertiesComponent properties = PropertiesComponent.getInstance();
/*  34 */     String storedSessionID = properties.getValue("augment.session.id");
/*  35 */     if (storedSessionID != null && !isBlank(storedSessionID)) {
/*  36 */       return storedSessionID;
/*     */     }
/*  38 */     String installationID = PermanentInstallationID.get();
/*  39 */     if (!isBlank(installationID)) {
/*  40 */       return installationID;
/*     */     }
/*  42 */     String newSessionID = UUID.randomUUID().toString();
/*  43 */     properties.setValue("augment.session.id", newSessionID);
/*  44 */     return newSessionID;
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String resetSessionId() {
/*  50 */     String newSessionId = UUID.randomUUID().toString();
/*  51 */     PropertiesComponent.getInstance().setValue("augment.session.id", newSessionId);
/*  52 */     return newSessionId;
/*     */   }
/*     */   
/*     */   private boolean isBlank(@NotNull String str) {
/*  56 */     return str.trim().isEmpty();
/*     */   }
/*     */   @NotNull
/*     */   public String getStoredSessionId() {
/*  60 */     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
/*  61 */     if (storedSessionID != null && !isBlank(storedSessionID)) {
/*  62 */       return storedSessionID;
/*     */     }
/*  64 */     String installationID = PermanentInstallationID.get();
/*  65 */     if (installationID != null && !isBlank(installationID)) {
/*  66 */       return installationID;
/*     */     }
/*  68 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean hasValidSessionId() {
/*  74 */     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
/*  75 */     if (storedSessionID != null && !isBlank(storedSessionID)) {
/*  76 */       return true;
/*     */     }
/*  78 */     String installationID = PermanentInstallationID.get();
/*  79 */     return (installationID != null && !isBlank(installationID));
/*     */   }
/*     */ 
/*     */   
/*     */   public void clearStoredSessionId() {
/*  84 */     PropertiesComponent.getInstance().unsetValue("augment.session.id");
/*     */   }
/*     */   @NotNull
/*     */   public String getSessionIdSource() {
/*  88 */     TrialSessionManager trialManager = TrialSessionManager.getInstance();
/*  89 */     if (trialManager.hasValidTrialSession()) {
/*  90 */       return "TrialSession";
/*     */     }
/*  92 */     String storedSessionID = PropertiesComponent.getInstance().getValue("augment.session.id");
/*  93 */     if (storedSessionID != null && !isBlank(storedSessionID)) {
/*  94 */       return "PropertiesComponent";
/*     */     }
/*  96 */     String installationID = PermanentInstallationID.get();
/*  97 */     return !isBlank(installationID) ? "PermanentInstallationID" : "Generated";
/*     */   }
/*     */ 
/*     */   
/*     */   @NotNull
/*     */   public String getSessionIdInfo() {
/* 103 */     String sessionId = getSessionId();
/* 104 */     String source = getSessionIdSource();
/*     */     
/* 106 */     return String.format("SessionID: %s (Source: %s)", new Object[] { sessionId, source });
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\SessionId.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */