/*     */ package com.baaon.v1;
/*     */ 
/*     */ import com.intellij.openapi.application.Application;
/*     */ import com.intellij.openapi.application.ApplicationManager;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import java.lang.reflect.Field;
/*     */ import java.util.Map;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class UnsafeCrossPluginAccess
/*     */ {
/*  71 */   private static final Logger LOG = Logger.getInstance(UnsafeCrossPluginAccess.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Object b1(String pluginId, String serviceClassName) {
/*     */     try {
/* 161 */       Application app = ApplicationManager.getApplication();
/*     */ 
/*     */       
/* 164 */       Field servicesField = app.getClass().getDeclaredField("myServices");
/*     */ 
/*     */       
/* 167 */       servicesField.setAccessible(true);
/*     */ 
/*     */       
/* 170 */       Map<?, ?> services = (Map<?, ?>)servicesField.get(app);
/*     */ 
/*     */       
/* 173 */       for (Map.Entry<?, ?> entry : services.entrySet()) {
/* 174 */         Object service = entry.getValue();
/*     */ 
/*     */         
/* 177 */         String serviceClass = (service != null) ? service.getClass().getName() : "none";
/* 178 */         LOG.info("发现服务: " + serviceClass);
/*     */ 
/*     */         
/* 181 */         if (service != null && service.getClass().getName().equals(serviceClassName)) {
/* 182 */           LOG.info("找到匹配的服务: " + serviceClassName);
/* 183 */           return service;
/*     */         } 
/*     */       } 
/*     */       
/* 187 */       LOG.warn("未找到指定的服务: " + serviceClassName);
/* 188 */     } catch (Exception e) {
/*     */       
/* 190 */       LOG.error("获取服务时发生异常 - 插件ID: " + pluginId + ", 服务类: " + serviceClassName, e);
/*     */     } 
/*     */ 
/*     */     
/* 194 */     return null;
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v1\UnsafeCrossPluginAccess.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */