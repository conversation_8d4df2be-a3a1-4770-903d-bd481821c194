/*    */ package com.baaon.shaded.org.json;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class JSONParserConfiguration
/*    */   extends ParserConfiguration
/*    */ {
/*    */   protected JSONParserConfiguration clone() {
/* 17 */     return new JSONParserConfiguration();
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public JSONParserConfiguration withMaxNestingDepth(int maxNestingDepth) {
/* 23 */     return super.<JSONParserConfiguration>withMaxNestingDepth(maxNestingDepth);
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\shaded\org\json\JSONParserConfiguration.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */