/*    */ package com.baaon;
/*    */ 
/*    */ import java.security.MessageDigest;
/*    */ 
/*    */ public class SecuritySHA1Util
/*    */ {
/*    */   public static String shaEncode(String inStr) throws Exception {
/*  8 */     MessageDigest sha = null;
/*    */     try {
/* 10 */       sha = MessageDigest.getInstance("SHA");
/* 11 */     } catch (Exception e) {
/* 12 */       e.printStackTrace();
/* 13 */       return "";
/*    */     } 
/*    */     
/* 16 */     byte[] byteArray = inStr.getBytes("UTF-8");
/* 17 */     byte[] md5Bytes = sha.digest(byteArray);
/* 18 */     StringBuffer hexValue = new StringBuffer();
/* 19 */     for (int i = 0; i < md5Bytes.length; i++) {
/* 20 */       int val = md5Bytes[i] & 0xFF;
/* 21 */       if (val < 16) {
/* 22 */         hexValue.append("0");
/*    */       }
/* 24 */       hexValue.append(Integer.toHexString(val));
/*    */     } 
/* 26 */     return hexValue.toString();
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\SecuritySHA1Util.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */