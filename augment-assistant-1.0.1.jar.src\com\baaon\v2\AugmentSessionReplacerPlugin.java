/*    */ package com.baaon.v2;
/*    */ 
/*    */ import com.intellij.ide.AppLifecycleListener;
/*    */ import com.intellij.openapi.application.ApplicationManager;
/*    */ import com.intellij.openapi.diagnostic.Logger;
/*    */ import java.util.List;
/*    */ import org.jetbrains.annotations.NotNull;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class AugmentSessionReplacerPlugin
/*    */   implements AppLifecycleListener
/*    */ {
/* 15 */   private static final Logger LOG = Logger.getInstance(AugmentSessionReplacerPlugin.class);
/*    */   private static volatile boolean isInitialized = false;
/*    */   
/*    */   public AugmentSessionReplacerPlugin() {
/* 19 */     initialize();
/*    */   }
/*    */   
/*    */   public void appFrameCreated(@NotNull List<String> commandLineArgs) {
/* 23 */     initialize();
/*    */   }
/*    */   
/*    */   public void appStarted() {
/* 27 */     initialize();
/*    */   }
/*    */   
/*    */   private void initialize() {
/* 31 */     if (!isInitialized) {
/*    */       try {
/* 33 */         LOG.info("Starting Augment Session ID Replacer Plugin...");
/* 34 */         ApplicationManager.getApplication().executeOnPooledThread(() -> {
/*    */               try {
/*    */                 SessionIdReplacer replacer = new SessionIdReplacer();
/*    */                 if (replacer.replaceSessionIdClass()) {
/*    */                   LOG.info("Successfully replaced SessionId class");
/*    */                   isInitialized = true;
/*    */                 } else {
/*    */                   LOG.warn("Failed to replace SessionId class");
/*    */                 } 
/* 43 */               } catch (Exception e) {
/*    */                 
/*    */                 LOG.error("Error during SessionId class replacement", e);
/*    */               } 
/*    */             });
/* 48 */       } catch (Exception e) {
/* 49 */         LOG.error("Failed to initialize Augment Session ID Replacer", e);
/*    */       } 
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public static AugmentSessionReplacerPlugin getInstance() {
/* 56 */     return (AugmentSessionReplacerPlugin)ApplicationManager.getApplication().getService(AugmentSessionReplacerPlugin.class);
/*    */   }
/*    */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\AugmentSessionReplacerPlugin.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */