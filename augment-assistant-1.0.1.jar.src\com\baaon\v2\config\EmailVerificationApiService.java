/*     */ package com.baaon.v2.config;
/*     */ 
/*     */ import com.baaon.SecuritySHA1Util;
/*     */ import com.baaon.shaded.org.json.JSONException;
/*     */ import com.baaon.shaded.org.json.JSONObject;
/*     */ import com.intellij.openapi.diagnostic.Logger;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.InputStreamReader;
/*     */ import java.net.HttpURLConnection;
/*     */ import java.net.URL;
/*     */ import java.net.URLEncoder;
/*     */ import java.nio.charset.StandardCharsets;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EmailVerificationApiService
/*     */ {
/*  20 */   private static final Logger LOG = Logger.getInstance(EmailVerificationApiService.class);
/*     */   private static final String EMAIL_GENERATION_API_URL = "http://60.204.224.73:82/api/aug/email/generate";
/*     */   private static final String CODE_QUERY_API_URL = "http://60.204.224.73:82/api/aug/email/code/latest";
/*     */   private static final int CONNECT_TIMEOUT = 10000;
/*     */   private static final int READ_TIMEOUT = 10000;
/*     */   private static final String USER_AGENT = "Augment-Assistant-Plugin";
/*     */   
/*     */   public ApiResponse<String> generateEmail(String authCode) {
/*  28 */     if (authCode != null && !authCode.trim().isEmpty()) {
/*     */       try {
/*  30 */         ApiResponse<String> var7; long time = System.currentTimeMillis();
/*  31 */         String encode = SecuritySHA1Util.shaEncode("" + time + "10f2f96d89a32941edf01bxcz0a076f10");
/*  32 */         String apiUrl = "http://60.204.224.73:82/api/aug/email/generate?ts=" + time + "&sign=" + encode + "&authCode=" + URLEncoder.encode(authCode, StandardCharsets.UTF_8.toString());
/*  33 */         LOG.info("调用生成邮箱API: " + apiUrl);
/*  34 */         URL url = new URL(apiUrl);
/*  35 */         HttpURLConnection connection = (HttpURLConnection)url.openConnection();
/*     */ 
/*     */         
/*     */         try {
/*  39 */           connection.setRequestMethod("POST");
/*  40 */           connection.setConnectTimeout(10000);
/*  41 */           connection.setReadTimeout(10000);
/*  42 */           connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
/*  43 */           connection.setRequestProperty("Accept", "application/json");
/*  44 */           int responseCode = connection.getResponseCode();
/*  45 */           LOG.info("生成邮箱API响应码: " + responseCode);
/*  46 */           if (responseCode != 200) {
/*  47 */             LOG.warn("生成邮箱API调用失败，响应码: " + responseCode);
/*  48 */             ApiResponse<?> var13 = ApiResponse.failure("API调用失败，响应码: " + responseCode);
/*  49 */             return (ApiResponse)var13;
/*     */           } 
/*     */           
/*  52 */           String responseBody = readResponse(connection);
/*  53 */           LOG.info("生成邮箱API响应: " + responseBody);
/*  54 */           var7 = parseEmailResponse(responseBody);
/*     */         } finally {
/*  56 */           connection.disconnect();
/*     */         } 
/*     */         
/*  59 */         return var7;
/*  60 */       } catch (Exception e) {
/*  61 */         LOG.error("生成邮箱API调用异常", e);
/*  62 */         return ApiResponse.failure("网络请求异常: " + e.getMessage());
/*     */       } 
/*     */     }
/*  65 */     return ApiResponse.failure("授权码不能为空");
/*     */   }
/*     */ 
/*     */   
/*     */   public ApiResponse<String> queryVerificationCode(String email, String authCode) {
/*  70 */     if (email != null && !email.trim().isEmpty()) {
/*  71 */       if (authCode != null && !authCode.trim().isEmpty()) {
/*     */         try {
/*  73 */           ApiResponse<String> var8; String var10000 = URLEncoder.encode(email, StandardCharsets.UTF_8.toString());
/*  74 */           long time = System.currentTimeMillis();
/*  75 */           String encode = SecuritySHA1Util.shaEncode("" + time + "10f2f96d89a32941edf01bxcz0a076f10");
/*     */           
/*  77 */           String apiUrl = "http://60.204.224.73:82/api/aug/email/code/latest?ts=" + time + "&sign=" + encode + "&email=" + var10000 + "&authCode=" + URLEncoder.encode(authCode, StandardCharsets.UTF_8.toString());
/*  78 */           LOG.info("调用查询验证码API: " + apiUrl);
/*  79 */           URL url = new URL(apiUrl);
/*  80 */           HttpURLConnection connection = (HttpURLConnection)url.openConnection();
/*     */ 
/*     */           
/*     */           try {
/*  84 */             connection.setRequestMethod("POST");
/*  85 */             connection.setConnectTimeout(10000);
/*  86 */             connection.setReadTimeout(10000);
/*  87 */             connection.setRequestProperty("User-Agent", "Augment-Assistant-Plugin");
/*  88 */             connection.setRequestProperty("Accept", "application/json");
/*  89 */             int responseCode = connection.getResponseCode();
/*  90 */             LOG.info("查询验证码API响应码: " + responseCode);
/*  91 */             if (responseCode != 200) {
/*  92 */               LOG.warn("查询验证码API调用失败，响应码: " + responseCode);
/*  93 */               ApiResponse<?> var14 = ApiResponse.failure("API调用失败，响应码: " + responseCode);
/*  94 */               return (ApiResponse)var14;
/*     */             } 
/*     */             
/*  97 */             String responseBody = readResponse(connection);
/*  98 */             LOG.info("查询验证码API响应: " + responseBody);
/*  99 */             var8 = parseCodeResponse(responseBody);
/*     */           } finally {
/* 101 */             connection.disconnect();
/*     */           } 
/*     */           
/* 104 */           return var8;
/* 105 */         } catch (Exception e) {
/* 106 */           LOG.error("查询验证码API调用异常", e);
/* 107 */           return ApiResponse.failure("网络请求异常: " + e.getMessage());
/*     */         } 
/*     */       }
/* 110 */       return ApiResponse.failure("授权码不能为空");
/*     */     } 
/*     */     
/* 113 */     return ApiResponse.failure("邮箱地址不能为空");
/*     */   }
/*     */ 
/*     */   
/*     */   private String readResponse(HttpURLConnection connection) throws Exception {
/* 118 */     BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8)); 
/* 119 */     try { StringBuilder response = new StringBuilder();
/*     */       
/*     */       String line;
/* 122 */       while ((line = reader.readLine()) != null) {
/* 123 */         response.append(line);
/*     */       }
/*     */       
/* 126 */       String str1 = response.toString();
/* 127 */       reader.close(); return str1; }
/*     */     catch (Throwable throwable) { try { reader.close(); }
/*     */       catch (Throwable throwable1) { throwable.addSuppressed(throwable1); }
/*     */        throw throwable; }
/*     */      } private ApiResponse<String> parseEmailResponse(String responseBody) { try {
/* 132 */       JSONObject jsonResponse = new JSONObject(responseBody);
/* 133 */       if (jsonResponse.optBoolean("success", false)) {
/* 134 */         String data = jsonResponse.optString("data");
/* 135 */         if (data != null && !data.trim().isEmpty()) {
/* 136 */           return ApiResponse.success(data, "邮箱生成成功");
/*     */         }
/*     */       } 
/*     */       
/* 140 */       String message = jsonResponse.optString("message", "邮箱生成失败");
/* 141 */       return ApiResponse.failure(message);
/* 142 */     } catch (JSONException e) {
/* 143 */       LOG.error("解析邮箱响应JSON失败", (Throwable)e);
/* 144 */       return ApiResponse.failure("响应格式错误");
/*     */     }  }
/*     */ 
/*     */   
/*     */   private ApiResponse<String> parseCodeResponse(String responseBody) {
/*     */     try {
/* 150 */       JSONObject jsonResponse = new JSONObject(responseBody);
/* 151 */       if (jsonResponse.optBoolean("success", false)) {
/* 152 */         String data = jsonResponse.optString("data");
/* 153 */         if (data != null && !data.trim().isEmpty()) {
/* 154 */           return ApiResponse.success(data, "验证码查询成功");
/*     */         }
/*     */       } else {
/* 157 */         LOG.warn("验证码查询失败，响应: " + responseBody);
/* 158 */         return ApiResponse.failure("验证码查询失败:" + jsonResponse.optString("message"));
/*     */       } 
/*     */       
/* 161 */       String message = jsonResponse.optString("message", "验证码查询失败");
/* 162 */       return ApiResponse.failure(message);
/* 163 */     } catch (JSONException e) {
/* 164 */       LOG.error("解析验证码响应JSON失败", (Throwable)e);
/* 165 */       return ApiResponse.failure("响应格式错误");
/*     */     } 
/*     */   }
/*     */ }


/* Location:              C:\Users\<USER>\Desktop\augment-assistant-1.0.1\augment-assistant\augment-assistant-1.0.1.jar!\com\baaon\v2\config\EmailVerificationApiService.class
 * Java compiler version: 17 (61.0)
 * JD-Core Version:       1.1.3
 */